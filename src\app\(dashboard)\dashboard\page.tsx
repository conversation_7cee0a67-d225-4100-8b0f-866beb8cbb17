import { redirect } from 'next/navigation';
import { Suspense } from 'react';
import DashboardClient from './client';
import { getAuthenticatedUser, getDashboardData } from '@/lib/dashboard-server-utils';
import { LoadingIndicatorStopper } from '@/components/loading-indicator-stopper';
import ClientWrapper from './client-wrapper';

// Define cache configuration for the page
export const dynamic = 'force-dynamic'; // Force dynamic rendering
export const revalidate = 0; // Dynamic page, no static caching

export default async function DashboardPage() {
  // Get authenticated user
  const { user, error: userError } = await getAuthenticatedUser();

  if (userError || !user) {
    // Redirect to login page if user is not authenticated
    redirect('/login');
  }

  const userId = user.id;

  // Fetch all dashboard data on the server side
  const { summary, trades, metrics, goals, uniqueSymbols, metricValues } = await getDashboardData(userId);

  // Create a streaming response with Suspense boundaries for each section
  return (
    <div className="space-y-6">
      {/* Add a loading indicator stopper at the top level */}
      <LoadingIndicatorStopper />

      {/* Wrap the client component in a Suspense boundary */}
      <Suspense fallback={<ClientWrapper isLoading={true} />}>
        <DashboardClient
          initialSummary={summary}
          initialTrades={trades || []}
          initialFilteredTrades={trades || []} // Pass the same trades for now, filtering happens client-side
          initialMetrics={metrics || []}
          initialGoals={goals || []}
          userId={userId}
          initialStats={null} // Not used yet
          initialFilters={null} // Not used yet
          uniqueSymbols={uniqueSymbols}
          initialMetricValues={metricValues || {}}
        />
      </Suspense>
    </div>
  );
}
