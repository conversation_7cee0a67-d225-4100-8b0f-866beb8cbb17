"use client"

import dynamic from 'next/dynamic';
import { CustomMetric, Goal } from '@/types/metrics';
import { Trade } from '@/types/trade';

// Dynamically import the client component with no SSR
const MetricsGoalsClient = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  selectedAccountId: string | null;
  initialMetrics: CustomMetric[];
  initialGoals: Goal[];
  initialTrades: Trade[];
  initialMetricValues: Record<string, number>;
}

export default function ClientWrapper({ 
  userId, 
  selectedAccountId, 
  initialMetrics, 
  initialGoals, 
  initialTrades,
  initialMetricValues
}: ClientWrapperProps) {
  return (
    <MetricsGoalsClient
      userId={userId}
      selectedAccountId={selectedAccountId}
      initialMetrics={initialMetrics}
      initialGoals={initialGoals}
      initialTrades={initialTrades}
      initialMetricValues={initialMetricValues}
    />
  );
}
