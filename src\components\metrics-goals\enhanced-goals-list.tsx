"use client"

import { useState, useEffect } from "react"
import { Goal, CustomMetric } from "@/types/metrics"
import { calculateMetricValue, calculateMetricProgress, getMetricTemplateType } from "@/lib/metrics-service"
import { GOAL_TEMPLATES, getGoalTemplateById } from "@/lib/goal-templates"
import { format, differenceInDays, isAfter, isBefore } from "date-fns"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Plus, 
  Target,
  Calendar,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  Clock,
  AlertTriangle,
  Sparkles,
  BarChart3
} from "lucide-react"
import { cn } from "@/lib/utils"

interface EnhancedGoalsListProps {
  userId: string
  accountId: string | null
  goals: Goal[]
  metrics: CustomMetric[]
  trades: any[]
  onEdit: (goal: Goal) => void
  onDelete: (goalId: string) => void
  onAdd: () => void
  onViewMetric?: (metricId: string) => void
}

export function EnhancedGoalsList({
  userId,
  accountId,
  goals,
  metrics,
  trades,
  onEdit,
  onDelete,
  onAdd,
  onViewMetric
}: EnhancedGoalsListProps) {
  const [metricValues, setMetricValues] = useState<Record<string, number>>({})
  const [deleteGoal, setDeleteGoal] = useState<Goal | null>(null)

  // Calculate metric values when trades or metrics change
  useEffect(() => {
    const calculateValues = async () => {
      const values: Record<string, number> = {}

      for (const metric of metrics) {
        values[metric.id] = await calculateMetricValue(metric, trades, accountId || undefined)
      }

      setMetricValues(values)
    }

    calculateValues()
  }, [metrics, trades, accountId])

  const getGoalProgress = (goal: Goal): number => {
    if (goal.metric_id && metricValues[goal.metric_id] !== undefined) {
      const linkedMetric = metrics.find(m => m.id === goal.metric_id)
      if (linkedMetric) {
        const currentValue = metricValues[goal.metric_id]
        // Use the metric's target value and progress calculation logic
        const metricWithGoalTarget = { ...linkedMetric, target_value: goal.target_value }
        return calculateMetricProgress(metricWithGoalTarget, currentValue)
      }
    }
    // Fallback for goals without linked metrics
    return Math.min(100, Math.max(0, (goal.current_value / goal.target_value) * 100))
  }

  const getGoalStatus = (goal: Goal) => {
    const now = new Date()
    const endDate = new Date(goal.end_date)
    const startDate = new Date(goal.start_date)
    
    if (goal.is_completed) return 'completed'
    if (isAfter(now, endDate)) return 'overdue'
    if (isBefore(now, startDate)) return 'upcoming'
    
    const progress = getGoalProgress(goal)
    const daysRemaining = differenceInDays(endDate, now)
    const totalDays = differenceInDays(endDate, startDate)
    const expectedProgress = ((totalDays - daysRemaining) / totalDays) * 100
    
    if (progress >= expectedProgress * 0.8) return 'on_track'
    if (progress >= expectedProgress * 0.5) return 'behind'
    return 'at_risk'
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'completed':
        return { 
          label: 'Completed', 
          color: 'bg-emerald-500', 
          icon: CheckCircle, 
          variant: 'default' as const 
        }
      case 'on_track':
        return { 
          label: 'On Track', 
          color: 'bg-green-500', 
          icon: TrendingUp, 
          variant: 'default' as const 
        }
      case 'behind':
        return { 
          label: 'Behind', 
          color: 'bg-amber-500', 
          icon: Clock, 
          variant: 'secondary' as const 
        }
      case 'at_risk':
        return { 
          label: 'At Risk', 
          color: 'bg-red-500', 
          icon: AlertTriangle, 
          variant: 'destructive' as const 
        }
      case 'overdue':
        return { 
          label: 'Overdue', 
          color: 'bg-red-600', 
          icon: AlertTriangle, 
          variant: 'destructive' as const 
        }
      case 'upcoming':
        return { 
          label: 'Upcoming', 
          color: 'bg-blue-500', 
          icon: Calendar, 
          variant: 'outline' as const 
        }
      default:
        return { 
          label: 'Unknown', 
          color: 'bg-gray-500', 
          icon: Clock, 
          variant: 'outline' as const 
        }
    }
  }

  const formatValue = (value: number, metric?: CustomMetric): string => {
    const formattedValue = value.toFixed(2)
    if (metric?.is_percentage) return `${formattedValue}%`
    return formattedValue
  }

  const getLinkedMetric = (goal: Goal): CustomMetric | undefined => {
    return goal.metric_id ? metrics.find(m => m.id === goal.metric_id) : undefined
  }

  const getTemplateInfo = (goal: Goal) => {
    // Try to match with a template based on title or other properties
    return GOAL_TEMPLATES.find(template => 
      template.title === goal.title || 
      (template.targetValue === goal.target_value && template.description === goal.description)
    )
  }

  if (goals.length === 0) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <div className="flex flex-col items-center space-y-4">
            <div className="rounded-full bg-muted p-4">
              <Target className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">No Trading Goals</h3>
              <p className="text-muted-foreground max-w-md">
                Set specific, measurable goals to track your trading progress. 
                Choose from proven templates or create custom goals.
              </p>
            </div>
            <Button onClick={onAdd} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Set Your First Goal
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Group goals by status for better organization
  const groupedGoals = goals.reduce((acc, goal) => {
    const status = getGoalStatus(goal)
    if (!acc[status]) acc[status] = []
    acc[status].push(goal)
    return acc
  }, {} as Record<string, Goal[]>)

  const statusOrder = ['completed', 'on_track', 'behind', 'at_risk', 'overdue', 'upcoming']

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Trading Goals</h2>
          <p className="text-muted-foreground">
            Track your progress with {goals.length} active goal{goals.length !== 1 ? 's' : ''}
          </p>
        </div>
        <Button onClick={onAdd} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Goal
        </Button>
      </div>

      {/* Goals Overview Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        {statusOrder.map(status => {
          const statusGoals = groupedGoals[status] || []
          if (statusGoals.length === 0) return null
          
          const config = getStatusConfig(status)
          const Icon = config.icon
          
          return (
            <Card key={status}>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className={cn("p-2 rounded-full bg-muted")}>
                    <Icon className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{statusGoals.length}</p>
                    <p className="text-xs text-muted-foreground">{config.label}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Goals List */}
      <div className="space-y-4">
        {statusOrder.map(status => {
          const statusGoals = groupedGoals[status] || []
          if (statusGoals.length === 0) return null
          
          const config = getStatusConfig(status)
          
          return (
            <div key={status} className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <config.icon className="h-5 w-5" />
                {config.label} ({statusGoals.length})
              </h3>
              
              <div className="grid gap-4 md:grid-cols-2">
                {statusGoals.map((goal) => {
                  const progress = getGoalProgress(goal)
                  const linkedMetric = getLinkedMetric(goal)
                  const templateInfo = getTemplateInfo(goal)
                  const currentValue = goal.metric_id && metricValues[goal.metric_id] !== undefined 
                    ? metricValues[goal.metric_id] 
                    : goal.current_value
                  const daysRemaining = differenceInDays(new Date(goal.end_date), new Date())

                  return (
                    <Card key={goal.id} className="relative group hover:shadow-md transition-all border-border/50 dark:border-border">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1 flex-1">
                            <div className="flex items-center gap-2">
                              <CardTitle className="text-base">{goal.title}</CardTitle>
                              {templateInfo && (
                                <Badge variant="secondary" className="text-xs flex items-center gap-1">
                                  <Sparkles className="h-3 w-3" />
                                  Template
                                </Badge>
                              )}
                              <Badge variant={config.variant} className="text-xs">
                                {config.label}
                              </Badge>
                            </div>
                            {goal.description && (
                              <CardDescription className="text-sm">
                                {goal.description}
                              </CardDescription>
                            )}
                          </div>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => onEdit(goal)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              {linkedMetric && onViewMetric && (
                                <DropdownMenuItem onClick={() => onViewMetric(linkedMetric.id)}>
                                  <BarChart3 className="h-4 w-4 mr-2" />
                                  View Metric
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem 
                                onClick={() => setDeleteGoal(goal)}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="space-y-4">
                          {/* Progress Display */}
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Progress</span>
                              <span className="font-medium">{progress.toFixed(1)}%</span>
                            </div>
                            <Progress value={progress} className="h-2" />
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>
                                Current: {formatValue(currentValue, linkedMetric)}
                              </span>
                              <span>
                                Target: {formatValue(goal.target_value, linkedMetric)}
                              </span>
                            </div>
                          </div>

                          {/* Timeline */}
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{format(new Date(goal.start_date), "MMM d")} - {format(new Date(goal.end_date), "MMM d, yyyy")}</span>
                            </div>
                            {daysRemaining > 0 && (
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{daysRemaining} days left</span>
                              </div>
                            )}
                          </div>

                          {/* Linked Metric */}
                          {linkedMetric && (
                            <div className="bg-muted/50 rounded-md p-3">
                              <div className="text-xs text-muted-foreground mb-1">Linked Metric:</div>
                              <div className="text-sm font-medium">{linkedMetric.name}</div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteGoal} onOpenChange={() => setDeleteGoal(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Goal</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deleteGoal?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteGoal) {
                  onDelete(deleteGoal.id)
                  setDeleteGoal(null)
                }
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
