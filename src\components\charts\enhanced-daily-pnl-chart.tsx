"use client"

import { format, startOfDay } from "date-fns"
import { ChartTooltip } from "@/components/ui/chart-tooltip"
import { ChartContainer } from "@/components/ui/chart-container"
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
  ReferenceLine,
} from "recharts"

interface Trade {
  time_close: string
  profit: number
}

interface DailyPnLChartProps {
  trades: Trade[]
}

export function EnhancedDailyPnLChart({ trades }: DailyPnLChartProps) {
  // Group trades by day and sum profits, also count trades per day
  const dailyData = trades.reduce((acc, trade) => {
    // Convert to local date string (YYYY-MM-DD)
    const day = startOfDay(new Date(trade.time_close)).toISOString().split('T')[0]
    if (!acc[day]) {
      acc[day] = { pnl: 0, tradeCount: 0 }
    }
    acc[day].pnl += trade.profit
    acc[day].tradeCount += 1
    return acc
  }, {} as Record<string, { pnl: number; tradeCount: number }>)

  // Convert to array and sort by date
  const chartData = Object.entries(dailyData)
    .map(([date, data]) => ({
      date,
      pnl: parseFloat(data.pnl.toFixed(2)), // Limit decimal places
      tradeCount: data.tradeCount,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) // Ensure chronological order

  return (
    <ChartContainer>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} margin={{ top: 20, right: 20, bottom: 60, left: 60 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.4} />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => format(new Date(value), "MMM d")}
            stroke="hsl(var(--muted-foreground))"
            tick={(props) => {
              const { x, y, payload } = props;
              return (
                <g transform={`translate(${x},${y})`}>
                  <text
                    x={0}
                    y={0}
                    dy={16}
                    textAnchor="end"
                    fill="hsl(var(--muted-foreground))"
                    opacity={0.7}
                    fontSize={11}
                    transform="rotate(-45)"
                  >
                    {format(new Date(payload.value), "MMM d")}
                  </text>
                </g>
              );
            }}
            height={60}
            axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
          />
          <YAxis
            tickFormatter={(value) => `$${value.toLocaleString()}`}
            stroke="hsl(var(--muted-foreground))"
            tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))', opacity: 0.7 }}
            axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
          />
          <ReferenceLine
            y={0}
            stroke="rgba(100, 100, 100, 0.5)"
            strokeDasharray="3 3"
          />
          <Tooltip
            cursor={{ fill: 'transparent' }}
            content={({ active, payload }) => {
              if (!active || !payload || !payload.length) return null;

              const data = payload[0].payload;
              const date = format(new Date(data.date), "MMM d, yyyy");

              return (
                <div className="rounded-lg border bg-card/95 backdrop-blur p-2 shadow-lg dark:shadow-none text-xs">
                  <div className="font-medium text-muted-foreground mb-1">{date}</div>
                  <div className="grid grid-cols-2 gap-x-3 gap-y-0.5">
                    <span className="text-muted-foreground">P&L:</span>
                    <span className={`font-medium text-right ${
                      data.pnl >= 0 ? 'text-emerald-500' : 'text-rose-500'
                    }`}>
                      ${data.pnl.toLocaleString()}
                    </span>
                    <span className="text-muted-foreground">Trades:</span>
                    <span className="font-medium text-right">
                      {data.tradeCount}
                    </span>
                  </div>
                </div>
              );
            }}
          />
          <Bar
            dataKey="pnl"
            animationDuration={1000}
            animationEasing="ease-out"
            style={{ cursor: 'pointer' }}
          >
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.pnl >= 0 ? "#10b981" : "#ef4444"}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
