export interface MetricTemplate {
  id: string;
  name: string;
  description: string;
  formula: string;
  is_percentage: boolean;
  display_precision: number;
  is_higher_better: boolean;
  category: 'profitability' | 'risk' | 'efficiency' | 'consistency';
  explanation: string;
  example: string;
}

export const METRIC_TEMPLATES: MetricTemplate[] = [
  // Profitability Metrics
  {
    id: 'win_rate',
    name: 'Win Rate',
    description: 'Percentage of winning trades',
    formula: '(win_count / total_trades) * 100',
    is_percentage: true,
    display_precision: 2,
    is_higher_better: true,
    category: 'profitability',
    explanation: 'Win rate measures the percentage of trades that result in a profit. A higher win rate indicates better trade selection.',
    example: 'If you have 70 winning trades out of 100 total trades, your win rate is 70%.'
  },
  {
    id: 'profit_factor',
    name: 'Profit Factor',
    description: 'Ratio of gross profit to gross loss',
    formula: 'profit_sum / loss_sum',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: true,
    category: 'profitability',
    explanation: 'Profit factor compares total profits to total losses. A value above 1.0 indicates profitability.',
    example: 'If your total profits are $5000 and total losses are $2500, your profit factor is 2.0.'
  },
  {
    id: 'net_profit_margin',
    name: 'Net Profit Margin',
    description: 'Net profit as percentage of total volume',
    formula: '(net_profit / (profit_sum + loss_sum)) * 100',
    is_percentage: true,
    display_precision: 2,
    is_higher_better: true,
    category: 'profitability',
    explanation: 'Shows how much profit you make relative to your total trading volume.',
    example: 'If your net profit is $1000 from $10000 total volume, your margin is 10%.'
  },
  {
    id: 'average_trade_return',
    name: 'Average Trade Return',
    description: 'Average profit/loss per trade',
    formula: 'net_profit / total_trades',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: true,
    category: 'profitability',
    explanation: 'The average amount you make or lose per trade.',
    example: 'If your net profit is $2500 from 100 trades, your average return is $25 per trade.'
  },

  // Risk Management Metrics
  {
    id: 'risk_reward_ratio',
    name: 'Risk/Reward Ratio',
    description: 'Average win to average loss ratio',
    formula: 'avg_win / avg_loss',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: true,
    category: 'risk',
    explanation: 'Compares the average size of your wins to your losses. Higher values indicate better risk management.',
    example: 'If your average win is $150 and average loss is $75, your ratio is 2.0.'
  },
  {
    id: 'max_drawdown_pct',
    name: 'Maximum Drawdown %',
    description: 'Largest peak-to-trough decline',
    formula: 'max_drawdown_pct',
    is_percentage: true,
    display_precision: 2,
    is_higher_better: false,
    category: 'risk',
    explanation: 'The largest percentage decline from a peak to a trough in your account value.',
    example: 'If your account dropped from $10000 to $8500, your max drawdown is 15%.'
  },
  {
    id: 'loss_rate',
    name: 'Loss Rate',
    description: 'Percentage of losing trades',
    formula: '(loss_count / total_trades) * 100',
    is_percentage: true,
    display_precision: 2,
    is_higher_better: false,
    category: 'risk',
    explanation: 'The percentage of trades that result in a loss.',
    example: 'If you have 30 losing trades out of 100 total trades, your loss rate is 30%.'
  },

  // Efficiency Metrics
  {
    id: 'trades_per_day',
    name: 'Trading Frequency',
    description: 'Average number of trades per day',
    formula: 'total_trades / (avg_trade_duration / 1440)',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: false,
    category: 'efficiency',
    explanation: 'How frequently you trade. Not necessarily better when higher - depends on your strategy.',
    example: 'If you make 100 trades over 50 trading days, your frequency is 2 trades per day.'
  },
  {
    id: 'average_trade_duration_hours',
    name: 'Average Trade Duration (Hours)',
    description: 'Average time trades are held',
    formula: 'avg_trade_duration / 60',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: false,
    category: 'efficiency',
    explanation: 'How long you typically hold trades. Useful for understanding your trading style.',
    example: 'If your average trade duration is 240 minutes, that equals 4 hours.'
  },

  // Consistency Metrics
  {
    id: 'consistency_ratio',
    name: 'Consistency Ratio',
    description: 'Ratio of winning periods to total periods',
    formula: 'win_count / total_trades',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: true,
    category: 'consistency',
    explanation: 'Measures how consistently you achieve positive results.',
    example: 'A ratio of 0.65 means you win 65% of the time.'
  },
  {
    id: 'largest_win_to_avg_win',
    name: 'Largest Win Ratio',
    description: 'Ratio of largest win to average win',
    formula: 'max_win / avg_win',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: false,
    category: 'consistency',
    explanation: 'Shows if your largest win is an outlier. Lower values indicate more consistent wins.',
    example: 'If your largest win is $500 and average win is $100, the ratio is 5.0.'
  },
  {
    id: 'largest_loss_to_avg_loss',
    name: 'Largest Loss Ratio',
    description: 'Ratio of largest loss to average loss',
    formula: 'max_loss / avg_loss',
    is_percentage: false,
    display_precision: 2,
    is_higher_better: false,
    category: 'consistency',
    explanation: 'Shows if your largest loss is an outlier. Lower values indicate better risk control.',
    example: 'If your largest loss is $200 and average loss is $50, the ratio is 4.0.'
  }
];

export const METRIC_CATEGORIES = [
  {
    id: 'profitability',
    name: 'Profitability',
    description: 'Metrics that measure how profitable your trading is',
    icon: '💰'
  },
  {
    id: 'risk',
    name: 'Risk Management',
    description: 'Metrics that measure how well you manage risk',
    icon: '🛡️'
  },
  {
    id: 'efficiency',
    name: 'Efficiency',
    description: 'Metrics that measure trading efficiency and frequency',
    icon: '⚡'
  },
  {
    id: 'consistency',
    name: 'Consistency',
    description: 'Metrics that measure consistency of performance',
    icon: '📊'
  }
] as const;

export function getTemplatesByCategory(category: string): MetricTemplate[] {
  return METRIC_TEMPLATES.filter(template => template.category === category);
}

export function getTemplateById(id: string): MetricTemplate | undefined {
  return METRIC_TEMPLATES.find(template => template.id === id);
}
