import { getSupabase<PERSON>rowser } from './supabase'
import { CustomMetric, Goal, METRIC_VARIABLES } from '@/types/metrics'
import { Trade } from '@/types/trade'

// Function to get all custom metrics for a user and account
export async function getCustomMetrics(userId: string, accountId?: string | null): Promise<CustomMetric[]> {
  const supabase = getSupabaseBrowser()

  try {
    let query = supabase
      .from('custom_metrics')
      .select('*')
      .eq('user_id', userId)

    // Add account filtering if accountId is provided
    if (accountId) {
      query = query.eq('account_id', accountId)
    } else {
      // If no accountId provided, get metrics without account_id (legacy support)
      query = query.is('account_id', null)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching custom metrics:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getCustomMetrics:', error)
    return []
  }
}

// Function to get a single custom metric by ID
export async function getCustomMetricById(userId: string, metricId: string): Promise<CustomMetric | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('custom_metrics')
      .select('*')
      .eq('id', metricId)
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching custom metric:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in getCustomMetricById:', error)
    return null
  }
}

// Function to create a new custom metric
export async function createCustomMetric(userId: string, metric: Omit<CustomMetric, 'id' | 'user_id' | 'created_at' | 'updated_at'>, accountId?: string | null): Promise<CustomMetric | null> {
  const supabase = getSupabaseBrowser()

  try {
    // Validate account ownership if accountId is provided
    if (accountId) {
      const { data: accountData, error: accountError } = await supabase
        .from('trading_accounts')
        .select('id')
        .eq('id', accountId)
        .eq('user_id', userId)
        .single()

      if (accountError || !accountData) {
        console.error('Account validation failed:', accountError)
        throw new Error('Invalid account ID or account does not belong to user')
      }
    }
    const insertData = {
      user_id: userId,
      account_id: accountId || null,
      ...metric
    }

    console.log('Creating custom metric with data:', insertData)

    const { data, error } = await supabase
      .from('custom_metrics')
      .insert([insertData])
      .select()
      .single()

    if (error) {
      console.error('Error creating custom metric:', {
        error,
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      })
      return null
    }

    return data
  } catch (error) {
    console.error('Error in createCustomMetric:', error)
    return null
  }
}

// Function to update an existing custom metric
export async function updateCustomMetric(userId: string, metricId: string, updates: Partial<Omit<CustomMetric, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<CustomMetric | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('custom_metrics')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', metricId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating custom metric:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in updateCustomMetric:', error)
    return null
  }
}

// Function to delete a custom metric
export async function deleteCustomMetric(userId: string, metricId: string): Promise<boolean> {
  const supabase = getSupabaseBrowser()

  try {
    const { error } = await supabase
      .from('custom_metrics')
      .delete()
      .eq('id', metricId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting custom metric:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in deleteCustomMetric:', error)
    return false
  }
}

// Function to get all goals for a user and account
export async function getGoals(userId: string, accountId?: string | null): Promise<Goal[]> {
  const supabase = getSupabaseBrowser()

  try {
    let query = supabase
      .from('goals')
      .select('*, custom_metrics(*)')
      .eq('user_id', userId)

    // Add account filtering if accountId is provided
    if (accountId) {
      query = query.eq('account_id', accountId)
    } else {
      // If no accountId provided, get goals without account_id (legacy support)
      query = query.is('account_id', null)
    }

    const { data, error } = await query.order('end_date', { ascending: true })

    if (error) {
      console.error('Error fetching goals:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getGoals:', error)
    return []
  }
}

// Function to get a single goal by ID
export async function getGoalById(userId: string, goalId: string): Promise<Goal | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('goals')
      .select('*, custom_metrics(*)')
      .eq('id', goalId)
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching goal:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in getGoalById:', error)
    return null
  }
}

// Function to create a new goal
export async function createGoal(userId: string, goal: Omit<Goal, 'id' | 'user_id' | 'created_at' | 'updated_at'>, accountId?: string | null): Promise<Goal | null> {
  const supabase = getSupabaseBrowser()

  try {
    // Validate account ownership if accountId is provided
    if (accountId) {
      const { data: accountData, error: accountError } = await supabase
        .from('trading_accounts')
        .select('id')
        .eq('id', accountId)
        .eq('user_id', userId)
        .single()

      if (accountError || !accountData) {
        console.error('Account validation failed for goal:', accountError)
        throw new Error('Invalid account ID or account does not belong to user')
      }
    }
    const { data, error } = await supabase
      .from('goals')
      .insert([
        {
          user_id: userId,
          account_id: accountId || null,
          ...goal
        }
      ])
      .select()
      .single()

    if (error) {
      console.error('Error creating goal:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in createGoal:', error)
    return null
  }
}

// Function to update an existing goal
export async function updateGoal(userId: string, goalId: string, updates: Partial<Omit<Goal, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<Goal | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('goals')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', goalId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating goal:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in updateGoal:', error)
    return null
  }
}

// Function to delete a goal
export async function deleteGoal(userId: string, goalId: string): Promise<boolean> {
  const supabase = getSupabaseBrowser()

  try {
    const { error } = await supabase
      .from('goals')
      .delete()
      .eq('id', goalId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting goal:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in deleteGoal:', error)
    return false
  }
}

// Function to calculate drawdown from trades
function calculateDrawdown(trades: Trade[]): { maxDrawdown: number; maxDrawdownPct: number } {
  if (trades.length === 0) return { maxDrawdown: 0, maxDrawdownPct: 0 }

  // Sort trades by close time
  const sortedTrades = [...trades].sort((a, b) =>
    new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
  )

  let runningBalance = 0
  let peak = 0
  let maxDrawdown = 0
  let maxDrawdownPct = 0

  for (const trade of sortedTrades) {
    runningBalance += trade.profit

    // Update peak if we have a new high
    if (runningBalance > peak) {
      peak = runningBalance
    }

    // Calculate current drawdown
    const currentDrawdown = peak - runningBalance
    const currentDrawdownPct = peak > 0 ? (currentDrawdown / peak) * 100 : 0

    // Update max drawdown
    if (currentDrawdown > maxDrawdown) {
      maxDrawdown = currentDrawdown
    }

    if (currentDrawdownPct > maxDrawdownPct) {
      maxDrawdownPct = currentDrawdownPct
    }
  }

  return { maxDrawdown, maxDrawdownPct }
}

// Function to calculate the value of a custom metric based on trades
export function calculateMetricValue(metric: CustomMetric, trades: Trade[]): number {
  try {
    // Calculate basic metrics from trades
    const winningTrades = trades.filter(trade => trade.profit > 0)
    const losingTrades = trades.filter(trade => trade.profit < 0)

    // Calculate drawdown
    const { maxDrawdown, maxDrawdownPct } = calculateDrawdown(trades)

    // Create a context object with all the variables that can be used in formulas
    const context: Record<string, number> = {
      win_count: winningTrades.length,
      loss_count: losingTrades.length,
      total_trades: trades.length,
      win_rate: trades.length > 0 ? winningTrades.length / trades.length : 0,
      profit_sum: winningTrades.reduce((sum, trade) => sum + trade.profit, 0),
      loss_sum: Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0)),
      net_profit: trades.reduce((sum, trade) => sum + trade.profit, 0),
      avg_win: winningTrades.length > 0 ? winningTrades.reduce((sum, trade) => sum + trade.profit, 0) / winningTrades.length : 0,
      avg_loss: losingTrades.length > 0 ? Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0) / losingTrades.length) : 0,
      max_win: winningTrades.length > 0 ? Math.max(...winningTrades.map(trade => trade.profit)) : 0,
      max_loss: losingTrades.length > 0 ? Math.abs(Math.min(...losingTrades.map(trade => trade.profit))) : 0,
      avg_trade: trades.length > 0 ? trades.reduce((sum, trade) => sum + trade.profit, 0) / trades.length : 0,
      max_drawdown: maxDrawdown,
      max_drawdown_pct: maxDrawdownPct,
      avg_trade_duration: trades.length > 0 ?
        trades.reduce((sum, trade) => {
          const duration = trade.durationMinutes || 0
          return sum + duration
        }, 0) / trades.length : 0,
    }

    // Create a function that evaluates the formula with the context
    const formula = metric.formula
    // Using Function constructor to create a function that has access to the context variables
    // This is safe as we control the formula and it's not user-provided
    const evalFunction = new Function(...Object.keys(context), `return ${formula}`)
    
    // Execute the function with the context values
    const result = evalFunction(...Object.values(context))

    // Always round to exactly 2 decimal places for consistency
    return parseFloat(result.toFixed(2))
  } catch (error) {
    console.error('Error calculating metric value:', error)
    return 0
  }
}

// Function to update the current value of a goal based on trades
export async function updateGoalProgress(userId: string, goalId: string, trades: Trade[]): Promise<Goal | null> {
  try {
    // Get the goal
    const goal = await getGoalById(userId, goalId)
    if (!goal || !goal.metric_id) return null
    
    // Get the associated metric
    const metric = await getCustomMetricById(userId, goal.metric_id)
    if (!metric) return null
    
    // Calculate the current value
    const currentValue = calculateMetricValue(metric, trades)
    
    // Check if the goal is completed
    const isCompleted = metric.is_higher_better 
      ? currentValue >= goal.target_value 
      : currentValue <= goal.target_value
    
    // Update the goal
    return updateGoal(userId, goalId, {
      current_value: currentValue,
      is_completed: isCompleted
    })
  } catch (error) {
    console.error('Error updating goal progress:', error)
    return null
  }
}

// Function to validate a metric formula
export function validateMetricFormula(formula: string): { isValid: boolean; error?: string } {
  try {
    // Create a context with dummy values for all variables
    const context: Record<string, number> = {}
    METRIC_VARIABLES.forEach(variable => {
      context[variable.name] = 1 // Dummy value
    })
    
    // Try to evaluate the formula
    const evalFunction = new Function(...Object.keys(context), `return ${formula}`)
    evalFunction(...Object.values(context))
    
    return { isValid: true }
  } catch (error) {
    return { 
      isValid: false, 
      error: `Invalid formula: ${(error as Error).message}` 
    }
  }
}
