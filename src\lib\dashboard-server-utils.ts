import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import { filterTrades } from './filter-utils';
import { DashboardFilters } from '@/components/dashboard/dashboard-filters';
import { isWeekend } from 'date-fns';

/**
 * Create a server-side Supabase client
 */
export async function getServerSupabase() {
  const cookieStore = await cookies();
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );
}

/**
 * Get the authenticated user from Supabase
 */
export async function getAuthenticatedUser() {
  const supabase = await getServerSupabase();
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    return { user: null, error };
  }
  
  return { user, error: null };
}

/**
 * Get user accounts from Supabase
 */
export async function getUserAccounts(userId: string) {
  const supabase = await getServerSupabase();
  
  const { data: accounts, error } = await supabase
    .from("trading_accounts")
    .select("*")
    .eq("user_id", userId)
    .order("updated_at", { ascending: false });
    
  return { accounts, error };
}

/**
 * Get the selected account ID from user preferences
 */
export async function getSelectedAccountId(userId: string) {
  const { accounts, error } = await getUserAccounts(userId);
  
  if (error || !accounts || accounts.length === 0) {
    return null;
  }
  
  return accounts[0].id;
}

/**
 * Get trading summary for an account
 */
export async function getTradingSummary(accountId: string) {
  const supabase = await getServerSupabase();
  
  const { data: summary, error } = await supabase
    .from("trading_summaries")
    .select("*")
    .eq("account_id", accountId)
    .maybeSingle();
    
  return { summary, error };
}

/**
 * Get trades for an account
 */
export async function getAccountTrades(accountId: string) {
  const supabase = await getServerSupabase();
  
  const { data: trades, error } = await supabase
    .from("trades")
    .select("*")
    .eq("account_id", accountId)
    .order("time_close", { ascending: false });
    
  return { trades: trades || [], error };
}

/**
 * Get custom metrics for a user
 */
export async function getCustomMetrics(userId: string) {
  const supabase = await getServerSupabase();
  
  const { data: metrics, error } = await supabase
    .from('custom_metrics')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
    
  return { metrics: metrics || [], error };
}

/**
 * Get goals for a user
 */
export async function getGoals(userId: string) {
  const supabase = await getServerSupabase();
  
  const { data: goals, error } = await supabase
    .from('goals')
    .select('*, custom_metrics(*)')
    .eq('user_id', userId)
    .order('end_date', { ascending: true });
    
  return { goals: goals || [], error };
}

/**
 * Get all unique symbols from trades
 */
export function getUniqueSymbols(trades: any[]) {
  const symbols = new Set<string>();
  
  trades.forEach(trade => {
    if (trade.symbol) {
      symbols.add(trade.symbol);
    }
  });
  
  return Array.from(symbols).sort();
}

/**
 * Filter trades on the server side
 */
export function serverFilterTrades(trades: any[], filters: DashboardFilters) {
  return filterTrades(trades, filters);
}

/**
 * Get custom metrics for a user and account (server-side)
 */
export async function getServerCustomMetrics(userId: string, accountId?: string | null) {
  const supabase = await getServerSupabase();

  let query = supabase
    .from('custom_metrics')
    .select('*')
    .eq('user_id', userId);

  // Add account filtering if accountId is provided
  if (accountId) {
    query = query.eq('account_id', accountId);
  } else {
    // If no accountId provided, get metrics without account_id (legacy support)
    query = query.is('account_id', null);
  }

  const { data: metrics, error } = await query.order('created_at', { ascending: false });

  return { metrics: metrics || [], error };
}

/**
 * Get goals for a user and account (server-side)
 */
export async function getServerGoals(userId: string, accountId?: string | null) {
  const supabase = await getServerSupabase();

  let query = supabase
    .from('goals')
    .select('*, custom_metrics(*)')
    .eq('user_id', userId);

  // Add account filtering if accountId is provided
  if (accountId) {
    query = query.eq('account_id', accountId);
  } else {
    // If no accountId provided, get goals without account_id (legacy support)
    query = query.is('account_id', null);
  }

  const { data: goals, error } = await query.order('end_date', { ascending: true });

  return { goals: goals || [], error };
}

/**
 * Calculate metric value on server-side
 */
export async function calculateServerMetricValue(metric: any, trades: any[], accountId?: string): Promise<number> {
  try {
    // Get pre-calculated metrics from trading_summaries if available
    let tradingSummary = null;
    if (accountId) {
      const supabase = await getServerSupabase();
      const { data } = await supabase
        .from('trading_summaries')
        .select('*')
        .eq('account_id', accountId)
        .single();

      tradingSummary = data;
    }

    // Calculate basic metrics from trades
    const winningTrades = trades.filter(trade => trade.profit > 0);
    const losingTrades = trades.filter(trade => trade.profit < 0);
    const totalTrades = trades.length;
    const winCount = winningTrades.length;
    const lossCount = losingTrades.length;

    // Calculate profit metrics
    const totalProfit = trades.reduce((sum, trade) => sum + trade.profit, 0);
    const grossProfit = winningTrades.reduce((sum, trade) => sum + trade.profit, 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0));

    // Calculate trade duration metrics
    const tradeDurations = trades.map(trade => {
      const openTime = new Date(trade.time_open).getTime();
      const closeTime = new Date(trade.time_close).getTime();
      return (closeTime - openTime) / (1000 * 60); // Duration in minutes
    });
    const avgTradeDuration = tradeDurations.length > 0
      ? tradeDurations.reduce((sum, duration) => sum + duration, 0) / tradeDurations.length
      : 0;

    // Calculate maximum drawdown
    let balance = tradingSummary?.initial_balance || 10000;
    let peak = balance;
    let maxDrawdown = 0;

    const sortedTrades = [...trades].sort((a, b) =>
      new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
    );

    sortedTrades.forEach(trade => {
      balance += trade.profit;
      peak = Math.max(peak, balance);
      const drawdown = peak - balance;
      const drawdownPercent = peak > 0 ? (drawdown / peak) * 100 : 0;
      maxDrawdown = Math.max(maxDrawdown, drawdownPercent);
    });

    // Create variables object for formula evaluation
    const variables: Record<string, number> = {
      total_trades: totalTrades,
      win_count: winCount,
      loss_count: lossCount,
      total_profit: totalProfit,
      gross_profit: grossProfit,
      gross_loss: grossLoss,
      avg_trade_duration: avgTradeDuration,
      max_drawdown_pct: maxDrawdown,
      profit_factor: grossLoss > 0 ? grossProfit / grossLoss : (grossProfit > 0 ? 999 : 0),
      // Use pre-calculated values from trading_summaries if available
      ...(tradingSummary && {
        total_trades: tradingSummary.total_trades || totalTrades,
        win_count: parseFloat(tradingSummary.profit_trades?.replace('%', '') || '0') * totalTrades / 100,
        loss_count: parseFloat(tradingSummary.loss_trades?.replace('%', '') || '0') * totalTrades / 100,
        total_profit: tradingSummary.total_net_profit || totalProfit,
        gross_profit: tradingSummary.gross_profit || grossProfit,
        gross_loss: tradingSummary.gross_loss || grossLoss,
        profit_factor: tradingSummary.profit_factor || (grossLoss > 0 ? grossProfit / grossLoss : (grossProfit > 0 ? 999 : 0)),
        max_drawdown_pct: tradingSummary.balance_drawdown_relative ?
          parseFloat(tradingSummary.balance_drawdown_relative.split('(')[1]?.replace(')', '').replace('%', '') || '0') : maxDrawdown
      })
    };

    // Evaluate the formula
    const result = evaluateFormula(metric.formula, variables);
    return isNaN(result) ? 0 : result;
  } catch (error) {
    console.error('Error calculating server metric value:', error);
    return 0;
  }
}

/**
 * Simple formula evaluator for metric calculations
 */
function evaluateFormula(formula: string, variables: Record<string, number>): number {
  try {
    // Replace variable names with their values
    let expression = formula;

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\b${key}\\b`, 'g');
      expression = expression.replace(regex, value.toString());
    });

    // Basic safety check - only allow numbers, operators, and parentheses
    if (!/^[0-9+\-*/.() ]+$/.test(expression)) {
      console.warn('Invalid formula expression:', expression);
      return 0;
    }

    // Evaluate the expression
    return Function(`"use strict"; return (${expression})`)();
  } catch (error) {
    console.error('Error evaluating formula:', error);
    return 0;
  }
}

/**
 * Get all dashboard data for a user
 */
export async function getDashboardData(userId: string) {
  // Get selected account ID
  const accountId = await getSelectedAccountId(userId);

  if (!accountId) {
    return {
      summary: null,
      trades: [],
      metrics: [],
      goals: [],
      uniqueSymbols: [],
      metricValues: {},
    };
  }

  // Get trading summary
  const { summary } = await getTradingSummary(accountId);

  // Get trades
  const { trades } = await getAccountTrades(accountId);

  // Get custom metrics (account-specific)
  const { metrics } = await getServerCustomMetrics(userId, accountId);

  // Get goals (account-specific)
  const { goals } = await getServerGoals(userId, accountId);

  // Pre-calculate metric values
  const metricValues: Record<string, number> = {};
  for (const metric of metrics) {
    metricValues[metric.id] = await calculateServerMetricValue(metric, trades, accountId);
  }

  // Get unique symbols
  const uniqueSymbols = getUniqueSymbols(trades);

  return {
    summary,
    trades,
    metrics,
    goals,
    uniqueSymbols,
    metricValues,
  };
}
