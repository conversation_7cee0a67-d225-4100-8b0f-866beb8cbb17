"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { CustomMetric } from "@/types/metrics"
import { createCustomMetric, updateCustomMetric, validateMetricFormula } from "@/lib/metrics-service"
import { METRIC_TEMPLATES, METRIC_CATEGORIES, getTemplatesByCategory, getTemplateById, MetricTemplate } from "@/lib/metric-templates"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { InfoIcon, Sparkles, Calculator } from "lucide-react"

// Define the form schema with enforced 2 decimal precision
const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(50, "Name must be less than 50 characters"),
  description: z.string().optional(),
  formula: z.string().min(1, "Formula is required"),
  is_percentage: z.boolean(),
  display_precision: z.literal(2), // Always 2 decimal places
  is_higher_better: z.boolean(),
  target_value: z.coerce.number().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface EnhancedMetricFormProps {
  userId: string
  accountId?: string | null
  metric?: CustomMetric
  onSuccess?: (metric: CustomMetric) => void
  onCancel?: () => void
}

export function EnhancedMetricForm({ userId, accountId, metric, onSuccess, onCancel }: EnhancedMetricFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formulaError, setFormulaError] = useState<string | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<MetricTemplate | null>(null)
  const [creationMode, setCreationMode] = useState<'template' | 'custom'>('template')

  const isEditing = !!metric

  // Initialize form with default values or existing metric values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: metric?.name || "",
      description: metric?.description || "",
      formula: metric?.formula || "",
      is_percentage: metric?.is_percentage || false,
      display_precision: 2, // Always 2 decimal places
      is_higher_better: metric?.is_higher_better || true,
      target_value: metric?.target_value || undefined,
    },
  })

  // Handle template selection
  const handleTemplateSelect = (template: MetricTemplate) => {
    setSelectedTemplate(template)
    form.setValue("name", template.name)
    form.setValue("description", template.description)
    form.setValue("formula", template.formula)
    form.setValue("is_percentage", template.is_percentage)
    form.setValue("display_precision", 2) // Always 2 decimal places
    form.setValue("is_higher_better", template.is_higher_better)
    setFormulaError(null)
  }

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)
    setFormulaError(null)

    try {
      // Validate the formula
      const validation = validateMetricFormula(values.formula)
      if (!validation.isValid) {
        setFormulaError(validation.error || "Invalid formula")
        setIsSubmitting(false)
        return
      }

      let result: CustomMetric | null

      if (isEditing && metric) {
        // Update existing metric
        result = await updateCustomMetric(userId, metric.id, values)
        if (result) {
          toast.success("Metric updated successfully")
        }
      } else {
        // Create new metric
        result = await createCustomMetric(userId, values, accountId)
        if (result) {
          toast.success("Metric created successfully")
          form.reset() // Reset form after successful creation
          setSelectedTemplate(null)
        } else {
          toast.error("Failed to create metric. Please check your account selection and try again.")
          return
        }
      }

      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (error) {
      console.error("Error saving metric:", error)
      const errorMessage = error instanceof Error ? error.message : "Failed to save metric"
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Validate formula on blur
  const validateFormula = () => {
    const formula = form.getValues("formula")
    if (formula) {
      const validation = validateMetricFormula(formula)
      if (!validation.isValid) {
        setFormulaError(validation.error || "Invalid formula")
      } else {
        setFormulaError(null)
      }
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          {isEditing ? "Edit Metric" : "Create New Metric"}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? "Update your custom trading metric"
            : "Choose from pre-built templates or create a custom metric to track your trading performance"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!isEditing && (
          <Tabs value={creationMode} onValueChange={(value) => setCreationMode(value as 'template' | 'custom')} className="mb-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="template" className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Use Template
              </TabsTrigger>
              <TabsTrigger value="custom" className="flex items-center gap-2">
                <Calculator className="h-4 w-4" />
                Custom Formula
              </TabsTrigger>
            </TabsList>

            <TabsContent value="template" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Choose a Metric Template</h3>
                <p className="text-sm text-muted-foreground">
                  Select from professionally designed trading metrics. Each template includes a proven formula and explanation.
                </p>
                
                {METRIC_CATEGORIES.map((category) => (
                  <div key={category.id} className="space-y-3">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{category.icon}</span>
                      <h4 className="font-medium">{category.name}</h4>
                      <Badge variant="outline" className="text-xs">{getTemplatesByCategory(category.id).length} metrics</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{category.description}</p>
                    
                    <div className="grid gap-3 md:grid-cols-2">
                      {getTemplatesByCategory(category.id).map((template) => (
                        <Card 
                          key={template.id} 
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedTemplate?.id === template.id ? 'ring-2 ring-primary' : ''
                          }`}
                          onClick={() => handleTemplateSelect(template)}
                        >
                          <CardContent className="p-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h5 className="font-medium">{template.name}</h5>
                                <Badge variant={template.is_percentage ? "secondary" : "outline"} className="text-xs">
                                  {template.is_percentage ? "%" : "#"}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{template.description}</p>
                              <div className="text-xs font-mono bg-muted p-2 rounded">
                                {template.formula}
                              </div>
                              <p className="text-xs text-muted-foreground">{template.explanation}</p>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Metric Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Win Rate %" {...field} />
                    </FormControl>
                    <FormDescription>
                      A short, descriptive name for your metric
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="target_value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Value (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="70.00"
                        {...field}
                        value={field.value === undefined ? "" : field.value}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : parseFloat(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      A target value for this metric (always displayed with 2 decimal places)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Percentage of winning trades out of total trades"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional description explaining what this metric measures
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedTemplate && (
              <Card className="bg-muted/50">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <h4 className="font-medium flex items-center gap-2">
                      <InfoIcon className="h-4 w-4" />
                      Template Information
                    </h4>
                    <p className="text-sm">{selectedTemplate.explanation}</p>
                    <p className="text-sm text-muted-foreground">
                      <strong>Example:</strong> {selectedTemplate.example}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            <FormField
              control={form.control}
              name="formula"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Formula</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="(win_count / total_trades) * 100"
                      className="font-mono h-24"
                      {...field}
                      onBlur={() => {
                        field.onBlur()
                        validateFormula()
                      }}
                    />
                  </FormControl>
                  {formulaError && (
                    <p className="text-sm font-medium text-destructive mt-1">{formulaError}</p>
                  )}
                  <FormDescription>
                    Define how this metric is calculated. All metrics display with exactly 2 decimal places.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="is_percentage"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Display as Percentage</FormLabel>
                      <FormDescription>
                        Show this metric with a % symbol
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_higher_better"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Higher is Better</FormLabel>
                      <FormDescription>
                        Is a higher value better for this metric?
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <CardFooter className="px-0 pb-0 pt-6 flex justify-between">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Update Metric"
                  : "Create Metric"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
