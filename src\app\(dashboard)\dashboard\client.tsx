"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { type ProcessedData } from "@/lib/excel-processor"
import type { Trade } from "@/types/trade"

// Dashboard-specific data type that uses database trades
interface DashboardData {
  account: {
    name: string
    account: string
    company: string
    date: string
  }
  trades: Trade[]
  summary: ProcessedData["summary"]
}
import { toast } from "sonner" // Keep this import for potential future use
import { AdvancedMetricsCard } from "@/components/advanced-metrics-card"
import { MetricsDashboard } from "@/components/metrics-goals/metrics-dashboard"
import { TradingCalendar } from "@/components/trading-calendar"
import { DashboardFilters, type DashboardFilters as DashboardFiltersType } from "@/components/dashboard/dashboard-filters"
import { filterTrades } from "@/lib/filter-utils"
// Import TradeDetailsDialog component
import { TradeDetailsDialog } from "@/components/dashboard/trade-details-dialog"


import { useAccount } from "@/contexts/account-context"
import { useSidebar } from "@/contexts/sidebar-context"
// Import React Query hooks
import { useDashboardStats, useDashboardTrades, useDashboardMetrics, useDashboardGoals } from "@/hooks/use-dashboard-data"

// Dashboard components
import { DashboardStats } from "@/components/dashboard/dashboard-stats"
import { DashboardCharts } from "@/components/dashboard/dashboard-charts"
import { DashboardSection } from "@/components/dashboard/dashboard-section"
import { CustomizableDashboard } from "@/components/dashboard/customizable-dashboard"
import { useDashboardLayout } from "@/hooks/use-dashboard-layout"
import { Settings, Layout } from "lucide-react"

// Define the props interface for the client component
interface DashboardClientProps {
  initialSummary: any | null;
  initialTrades: any[];
  initialFilteredTrades?: any[]; // Optional, for server-side filtered trades
  initialMetrics: any[];
  initialGoals: any[];
  userId: string;
  initialStats?: any | null; // Optional, for future use
  initialFilters?: any | null; // Optional, for future use
  uniqueSymbols?: string[]; // Optional, for server-side unique symbols
  initialMetricValues?: Record<string, number>; // Pre-computed metric values
}

export default function DashboardClient({
  initialSummary,
  initialTrades,
  initialFilteredTrades, // Kept for interface compatibility
  initialMetrics,
  initialGoals,
  userId,
  initialStats, // Kept for future use in stats components
  initialFilters, // Kept for interface compatibility
  uniqueSymbols: initialUniqueSymbols,
  initialMetricValues
}: DashboardClientProps) {
  console.log('[DashboardClient] Component rendered with SSR data:', {
    initialMetrics: initialMetrics?.length || 0,
    initialGoals: initialGoals?.length || 0,
    initialTrades: initialTrades?.length || 0,
    initialMetricValues: initialMetricValues ? Object.keys(initialMetricValues).length : 0,
    selectedAccountId
  });
  const router = useRouter()

  // userId is now passed from the server component
  const lastRefreshTimeRef = useRef<number>(Date.now())
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [isTradeDetailsOpen, setIsTradeDetailsOpen] = useState(false)

  // Effect to stop the loading indicator when the component mounts
  useEffect(() => {
    // Function to stop the loading indicator
    const stopLoadingIndicator = () => {
      // Method 1: Push state to history
      window.history.pushState(null, '', window.location.href)

      // Method 2: Dispatch navigation end event
      window.dispatchEvent(new Event('routeChangeComplete'))

      // Method 3: Force any NProgress instances to complete
      if (typeof window !== 'undefined') {
        // Try to access NProgress directly
        const anyWindow = window as any
        if (anyWindow.NProgress) {
          anyWindow.NProgress.done()
        }

        // Try to find and manipulate the loading bar element directly
        const loadingBar = document.getElementById('nprogress')
        if (loadingBar) {
          loadingBar.style.display = 'none'
        }
      }
    }

    // Execute immediately
    stopLoadingIndicator()

    // Then try multiple times with increasing delays
    const timers = [
      setTimeout(stopLoadingIndicator, 100),
      setTimeout(stopLoadingIndicator, 300),
      setTimeout(stopLoadingIndicator, 500),
      setTimeout(stopLoadingIndicator, 1000),
      setTimeout(stopLoadingIndicator, 2000)
    ]

    return () => {
      timers.forEach(timer => clearTimeout(timer))
    }
  }, [])

  // Advanced dashboard filters
  const [dashboardFilters, setDashboardFilters] = useState<DashboardFiltersType>({
    dateRange: undefined,
    timeFilter: "all",
    strategyId: null,
    tradeType: "all",
    symbols: [],
    showWeekends: true
  })

  const [selectedTrades, setSelectedTrades] = useState<Trade[]>([])
  const tradesPerPage = 10
  const { selectedAccountId, isAccountSwitching, isInitializing } = useAccount()
  const { isSidebarCollapsed } = useSidebar()

  // Customizable dashboard state
  const [isCustomizableMode, setIsCustomizableMode] = useState(false)
  const { layoutOrder, saveLayout, isLoading: isLayoutLoading } = useDashboardLayout(userId)

  // State for current metric values (updates when account changes)
  const [currentMetricValues, setCurrentMetricValues] = useState<Record<string, number>>(initialMetricValues || {})

  // Determine grid classes based on screen size and sidebar state
  const getMetricsGridClasses = () => {
    // Base: 1 column on mobile
    // md (768px+): 2 columns when sidebar expanded, 2 when collapsed (since it's already a 2-column layout)
    // lg (1024px+): 2 columns when sidebar expanded, 2 when collapsed
    // For the metrics grid, we keep it as 2 columns since it works well in both states
    return "grid gap-6 grid-cols-1 md:grid-cols-2 dashboard-metrics"
  }

  // Use React Query for data fetching with initial data from server
  const {
    data: summaryData,
    isLoading: isStatsLoading,
    refetch: refetchStats,
    error: statsError
  } = useDashboardStats(selectedAccountId, initialSummary)

  const {
    data: tradesData,
    isLoading: isTradesLoading,
    refetch: refetchTrades,
    error: tradesError
  } = useDashboardTrades(selectedAccountId, initialTrades)

  const {
    data: metricsData,
    isLoading: isMetricsLoading,
    refetch: refetchMetrics,
    error: metricsError
  } = useDashboardMetrics(selectedAccountId, 'metrics', initialMetrics)

  const {
    data: goalsData,
    isLoading: isGoalsLoading,
    refetch: refetchGoals,
    error: goalsError
  } = useDashboardGoals(selectedAccountId, initialGoals)

  // Log any errors for debugging
  useEffect(() => {
    if (statsError) console.error('Dashboard stats error:', statsError)
    if (tradesError) console.error('Dashboard trades error:', tradesError)
    if (metricsError) console.error('Dashboard metrics error:', metricsError)
    if (goalsError) console.error('Dashboard goals error:', goalsError)
  }, [statsError, tradesError, metricsError, goalsError])

  // Combine loading states
  const isAccountLoading = isStatsLoading || isTradesLoading || isMetricsLoading || isGoalsLoading
  // Show loading state when either data is loading or account is switching
  const isPageLoading = isAccountLoading || isAccountSwitching || isInitializing

  // Recalculate metric values when account changes or data updates
  useEffect(() => {
    const recalculateMetrics = async () => {
      if (!metricsData || !tradesData || !selectedAccountId) {
        console.log('[Dashboard] Skipping metric recalculation - missing data');
        return;
      }

      console.log('[Dashboard] Recalculating metric values for account:', selectedAccountId);
      const values: Record<string, number> = {};

      for (const metric of metricsData) {
        try {
          const { calculateMetricValue } = await import('@/lib/metric-calculator');
          const value = await calculateMetricValue(metric, tradesData, selectedAccountId);
          values[metric.id] = value;
        } catch (error) {
          console.error('[Dashboard] Error calculating metric value for', metric.name, ':', error);
          values[metric.id] = 0;
        }
      }

      console.log('[Dashboard] Updated metric values:', Object.keys(values).length, 'metrics');
      setCurrentMetricValues(values);
    };

    recalculateMetrics();
  }, [selectedAccountId, metricsData, tradesData])

  // Create tradeData object from React Query data
  const tradeData: DashboardData = {
    account: {
      name: "",
      account: "",
      company: "",
      date: new Date().toISOString(),
    },
    trades: tradesData || [],
    summary: summaryData || {
      // Provide default summary values if none available
      total_trades: tradesData?.length || 0,
      profit_trades: '0.00%',
      loss_trades: '0.00%',
      total_net_profit: 0,
      balance_drawdown_maximal: '0.00 (0.00%)',
    },
  }

  // Function to refetch all data
  const refetchAllData = () => {
    console.log('Refetching all dashboard data')
    refetchStats()
    refetchTrades()
    refetchMetrics()
    refetchGoals()
    lastRefreshTimeRef.current = Date.now()
  }

  // Update last refresh time and trigger data refetch when account changes
  useEffect(() => {
    console.log('Dashboard: Account changed to', selectedAccountId)
    lastRefreshTimeRef.current = Date.now()

    // Force refetch all data when account changes
    if (selectedAccountId) {
      console.log('Dashboard: Forcing data refetch due to account change')
      refetchAllData()
    }
  }, [selectedAccountId])

  // Add a listener to refresh data when returning to the dashboard
  useEffect(() => {
    // Use the ref for tracking last refresh time to prevent too frequent refreshes
    // This ensures the time is shared across all effects
    let tabHiddenTime: number | null = null
    const REFRESH_THRESHOLD = 5000 // 5 seconds between refreshes
    const AWAY_THRESHOLD = 180000   // 3 minutes away to trigger refresh

    // Function to handle visibility changes with time away tracking
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // User left the tab - record the time
        tabHiddenTime = Date.now()
        console.log(`Dashboard: Tab hidden at: ${new Date(tabHiddenTime).toLocaleTimeString()}`)
      }
      else if (document.visibilityState === 'visible') {
        // User returned to the tab
        const now = Date.now()

        if (tabHiddenTime) {
          const timeAway = now - tabHiddenTime
          console.log(`Dashboard: Tab visible again. Was hidden for ${Math.round(timeAway/1000)} seconds. Threshold is ${AWAY_THRESHOLD/1000} seconds.`)

          // Only refresh if they've been away for longer than AWAY_THRESHOLD
          // AND enough time has passed since last refresh
          if (timeAway > AWAY_THRESHOLD && (now - lastRefreshTimeRef.current > REFRESH_THRESHOLD)) {
            console.log(`Dashboard: Tab was hidden for ${Math.round(timeAway/1000)} seconds (>3 min), refreshing dashboard data...`)
            lastRefreshTimeRef.current = now
            // Refresh all data
            refetchAllData()
          } else {
            console.log(`Dashboard: Tab was hidden for ${Math.round(timeAway/1000)} seconds, skipping dashboard refresh (below 3 min threshold)`)
          }
        } else {
          console.log('Dashboard: Tab became visible but no hidden time was recorded')
        }

        // Reset the hidden time
        tabHiddenTime = null
      }
    }

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Also refresh when the window gets focus, but with the same threshold
    const handleFocus = () => {
      // We'll disable the focus handler since it's causing unwanted refreshes
      // The visibility change handler will take care of refreshes
      console.log('Dashboard: Window focused event - refresh disabled to prevent unwanted refreshes')
      // No refresh happens here anymore
    }

    // Refresh when navigating to dashboard
    const handleDashboardNavigation = () => {
      // We'll keep this handler active for navigation events
      const now = Date.now()
      // Only refresh if enough time has passed since last refresh
      if (now - lastRefreshTimeRef.current > REFRESH_THRESHOLD) {
        console.log('Navigated to dashboard, refreshing data...')
        // Refresh all data
        refetchAllData()
        lastRefreshTimeRef.current = now
      } else {
        console.log('Navigated to dashboard, but skipping refresh (too soon after last refresh)')
      }
    }

    window.addEventListener('focus', handleFocus)
    window.addEventListener('dashboardNavigated', handleDashboardNavigation)

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('dashboardNavigated', handleDashboardNavigation)
    }
  }, [selectedAccountId, refetchStats, refetchTrades, refetchMetrics, refetchGoals])

  const calculateRiskReward = (trades: Trade[]) => {
    const winningTrades = trades.filter(t => t.profit > 0)
    const losingTrades = trades.filter(t => t.profit < 0)

    const avgWin = winningTrades.length > 0
      ? winningTrades.reduce((sum, t) => sum + t.profit, 0) / winningTrades.length
      : 0

    const avgLoss = losingTrades.length > 0
      ? Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length)
      : 1 // Avoid division by zero

    return avgLoss > 0 ? avgWin / avgLoss : avgWin
  }

  // Get all unique symbols from trades - use server-provided data if available
  const uniqueSymbols = useMemo(() => {
    if (initialUniqueSymbols && initialUniqueSymbols.length > 0) {
      return initialUniqueSymbols;
    }

    if (!tradeData || !tradeData.trades) return []
    const symbols = new Set<string>()
    tradeData.trades.forEach(trade => {
      if (trade.symbol) {
        symbols.add(trade.symbol)
      }
    })
    return Array.from(symbols).sort()
  }, [tradeData, initialUniqueSymbols])

  // Apply dashboard filters to trades
  const filteredTrades = useMemo(() => {
    if (!tradeData || !tradeData.trades) return []
    return filterTrades(tradeData.trades, dashboardFilters)
  }, [tradeData, dashboardFilters])

  // Handle dashboard filter changes
  const handleFilterChange = (filters: DashboardFiltersType) => {
    setDashboardFilters(filters)
  }



  // Define dashboard sections for customizable layout
  const dashboardSections = useMemo(() => {
    if (!selectedAccountId || isPageLoading || isAccountLoading || isAccountSwitching || isInitializing) {
      console.log('Dashboard sections: returning empty array due to loading state')
      return []
    }

    console.log('Dashboard sections: creating sections for customizable layout')
    return [
      {
        id: 'stats',
        title: 'Performance Statistics',
        component: (
          <DashboardStats
            tradeData={{
              account: tradeData?.account || {
                name: "",
                account: "",
                company: "",
                date: new Date().toISOString(),
              },
              trades: filteredTrades,
              summary: tradeData?.summary || {
                total_trades: filteredTrades.length,
                profit_trades: '0.00%',
                loss_trades: '0.00%',
                total_net_profit: 0,
                gross_profit: 0,
                gross_loss: 0,
                profit_factor: 0,
                expected_payoff: 0,
                recovery_factor: 0,
                sharpe_ratio: 0,
                balance_drawdown_absolute: 0,
                balance_drawdown_maximal: '0.00 (0.00%)',
                balance_drawdown_relative: '0.00% (0.00)',
                short_trades_won: '0 (0.00%)',
                long_trades_won: '0 (0.00%)',
                largest_profit_trade: 0,
                largest_loss_trade: 0,
                average_profit_trade: 0,
                average_loss_trade: 0,
                maximum_consecutive_wins: '0 (0.00)',
                maximum_consecutive_losses: '0 (0.00)',
                maximal_consecutive_profit: '0.00 (0)',
                maximal_consecutive_loss: '0.00 (0)',
                average_consecutive_wins: 0,
                average_consecutive_losses: 0,
                initial_balance: 0
              }
            }}
            calculateRiskReward={calculateRiskReward}
          />
        )
      },
      {
        id: 'charts',
        title: 'Performance Charts',
        component: (
          <DashboardCharts
            trades={filteredTrades}
            initialBalance={tradeData?.summary?.initial_balance || 0}
          />
        )
      },
      {
        id: 'advanced-metrics',
        title: 'Advanced Metrics',
        component: <AdvancedMetricsCard trades={filteredTrades} />
      },
      {
        id: 'custom-metrics',
        title: 'Custom Metrics & Goals',
        component: (
          <MetricsDashboard
            userId={userId || ''}
            accountId={selectedAccountId}
            metrics={metricsData || []}
            goals={goalsData || []}
            trades={filteredTrades}
            onViewAll={() => router.push('/metrics-goals')}
            preComputedValues={currentMetricValues}
          />
        )
      },
      {
        id: 'calendar',
        title: 'Trading Calendar',
        component: (
          <TradingCalendar
            accountId={selectedAccountId}
            trades={filteredTrades}
            userId={userId || undefined}
            dashboardFilters={dashboardFilters}
            onSelectDate={(date, trades) => {
              setSelectedDate(date)
              setSelectedTrades(trades || [])
              setIsTradeDetailsOpen(true)
            }}
          />
        )
      }
    ]
  }, [
    selectedAccountId,
    isPageLoading,
    isAccountLoading,
    isAccountSwitching,
    isInitializing,
    tradeData,
    filteredTrades,
    calculateRiskReward,
    metricsData,
    goalsData,
    userId,
    router,
    dashboardFilters,
    currentMetricValues
  ])

  // Handle layout order changes
  const handleLayoutOrderChange = (newOrder: string[]) => {
    saveLayout(newOrder)
  }



  // Prefetch common navigation paths
  useEffect(() => {
    // Only prefetch if we have an account selected
    if (selectedAccountId) {
      // Prefetch common navigation paths
      const prefetchPaths = [
        '/trades',
        '/symbols',
        '/journal',
        '/metrics-goals',
        '/playbook'
      ];

      // Use the router to prefetch these paths
      prefetchPaths.forEach(path => {
        try {
          router.prefetch(path);
          console.log(`Prefetched: ${path}`);
        } catch (error) {
          console.error(`Failed to prefetch ${path}:`, error);
        }
      });
    }
  }, [selectedAccountId, router]);

  return (
    <div className="space-y-6">
      {/* Force the loading indicator to stop */}
      <script dangerouslySetInnerHTML={{ __html: `
        // Force the loading indicator to stop
        setTimeout(() => {
          // Method 1: Push state to history
          window.history.pushState(null, '', window.location.href);

          // Method 2: Dispatch navigation end event
          window.dispatchEvent(new Event('routeChangeComplete'));

          // Method 3: Force any NProgress instances to complete
          if (typeof window.NProgress !== 'undefined') {
            window.NProgress.done();
          }

          // Method 4: Find and hide the loading bar element
          const loadingBar = document.getElementById('nprogress');
          if (loadingBar) {
            loadingBar.style.display = 'none';
          }
        }, 500);
      ` }} />



      <div className="flex flex-col gap-4 mb-4 dashboard-header" data-section="header">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-4">
          <div className="flex items-center gap-2">
            {/* Dashboard Filters - moved from below */}
            {selectedAccountId !== null && tradeData && (
              <DashboardFilters
                userId={userId || undefined}
                symbols={uniqueSymbols}
                onFilterChange={handleFilterChange}
              />
            )}
            <Button
              variant={isCustomizableMode ? "default" : "outline"}
              size="sm"
              onClick={() => setIsCustomizableMode(!isCustomizableMode)}
              className="flex items-center gap-2"
            >
              <Layout className="h-4 w-4" />
              {isCustomizableMode ? "Exit Customize" : "Customize Layout"}
            </Button>

          </div>
        </div>
      </div>

      {selectedAccountId === null ? (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dropdown above to view your trading data, or go to the Accounts page to select an account.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/accounts')}
              className="mt-4"
            >
              Go to Accounts
            </Button>
          </div>
        </Card>
      ) : (
        <>
          {isPageLoading || isAccountLoading || isAccountSwitching || isInitializing ? (
            <div className="space-y-6">
              {/* Loading Skeleton for Stats */}
              <div className="dashboard-stats" data-section="stats">
                <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
                  {[...Array(5)].map((_, i) => (
                    <Card key={i} className="p-4 h-[120px]">
                      <div className="h-4 w-1/3 bg-muted rounded mb-4 slow-pulse"></div>
                      <div className="h-8 w-1/2 bg-muted rounded mb-2 slow-pulse"></div>
                      <div className="h-2 w-full bg-muted rounded-full slow-pulse"></div>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Loading Skeleton for Charts */}
              <div className="dashboard-charts" data-section="charts">
                <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
                  {[...Array(2)].map((_, i) => (
                    <Card key={i} className="p-4 h-[300px]">
                      <div className="h-4 w-1/3 bg-muted rounded mb-4 slow-pulse"></div>
                      <div className="h-[250px] w-full bg-muted rounded slow-pulse"></div>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          ) : isCustomizableMode ? (
            /* Customizable Dashboard Layout */
            <CustomizableDashboard
              sections={dashboardSections}
              onOrderChange={handleLayoutOrderChange}
              savedOrder={layoutOrder}
            />
          ) : (
            <>
              {/* Static Dashboard Layout */}
              {/* Stats Grid */}
              <div className="dashboard-stats" data-section="stats">
                <DashboardStats
                  tradeData={{
                    account: tradeData?.account || {
                      name: "",
                      account: "",
                      company: "",
                      date: new Date().toISOString(),
                    },
                    trades: filteredTrades,
                    summary: tradeData?.summary || {
                      total_trades: filteredTrades.length,
                      profit_trades: '0.00%',
                      loss_trades: '0.00%',
                      total_net_profit: 0,
                      gross_profit: 0,
                      gross_loss: 0,
                      profit_factor: 0,
                      expected_payoff: 0,
                      recovery_factor: 0,
                      sharpe_ratio: 0,
                      balance_drawdown_absolute: 0,
                      balance_drawdown_maximal: '0.00 (0.00%)',
                      balance_drawdown_relative: '0.00% (0.00)',
                      short_trades_won: '0 (0.00%)',
                      long_trades_won: '0 (0.00%)',
                      largest_profit_trade: 0,
                      largest_loss_trade: 0,
                      average_profit_trade: 0,
                      average_loss_trade: 0,
                      maximum_consecutive_wins: '0 (0.00)',
                      maximum_consecutive_losses: '0 (0.00)',
                      maximal_consecutive_profit: '0.00 (0)',
                      maximal_consecutive_loss: '0.00 (0)',
                      average_consecutive_wins: 0,
                      average_consecutive_losses: 0,
                      initial_balance: 0
                    }
                  }}
                  calculateRiskReward={calculateRiskReward}
                />
              </div>

              {/* Charts Grid */}
              <div className="dashboard-charts" data-section="charts">
                <DashboardCharts
                  trades={filteredTrades}
                  initialBalance={tradeData?.summary?.initial_balance || 0}
                />
              </div>
            </>
          )}
        </>
      )}

      {selectedAccountId !== null && !isCustomizableMode && (
        <>
          {isPageLoading || isAccountLoading || isAccountSwitching || isInitializing ? (
            <>
              {/* Loading Skeleton for Advanced Metrics and Custom Metrics & Goals */}
              <div className="grid gap-6 grid-cols-1 md:grid-cols-2 dashboard-metrics">
                {/* Advanced Metrics Skeleton */}
                <div className="order-2 md:order-1 dashboard-advanced-metrics" id="dashboard-advanced-metrics" data-section="advanced-metrics">
                  <Card className="p-4 h-[400px]">
                    <div className="h-4 w-1/3 bg-muted rounded mb-4 slow-pulse"></div>
                    <div className="flex gap-2 mb-4">
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="h-8 w-20 bg-muted rounded slow-pulse"></div>
                      ))}
                    </div>
                    <div className="h-[300px] w-full bg-muted rounded slow-pulse"></div>
                  </Card>
                </div>

                {/* Custom Metrics & Goals Skeleton */}
                <div className="order-1 md:order-2 dashboard-custom-metrics" id="dashboard-custom-metrics" data-section="custom-metrics">
                  <Card className="p-4 h-[400px]">
                    <div className="h-4 w-1/3 bg-muted rounded mb-4 slow-pulse"></div>
                    <div className="h-[350px] w-full bg-muted rounded slow-pulse"></div>
                  </Card>
                </div>
              </div>

              {/* Loading Skeleton for Calendar */}
              <div className="dashboard-calendar" data-section="calendar">
                <Card className="p-4 h-[500px]">
                  <div className="h-4 w-1/3 bg-muted rounded mb-4 slow-pulse"></div>
                  <div className="h-[450px] w-full bg-muted rounded slow-pulse"></div>
                </Card>
              </div>
            </>
          ) : (
            <>
              {/* Advanced Metrics and Custom Metrics & Goals */}
              <div className={getMetricsGridClasses()}>
                {/* Advanced Metrics */}
                <div className="order-2 md:order-1 dashboard-advanced-metrics" id="dashboard-advanced-metrics" data-section="advanced-metrics">
                  <AdvancedMetricsCard trades={filteredTrades} />
                </div>

                {/* Custom Metrics & Goals */}
                <div className="order-1 md:order-2 dashboard-custom-metrics" id="dashboard-custom-metrics" data-section="custom-metrics">
                  <MetricsDashboard
                    userId={userId || ''}
                    accountId={selectedAccountId}
                    metrics={metricsData || []}
                    goals={goalsData || []}
                    trades={filteredTrades}
                    onViewAll={() => router.push('/metrics-goals')}
                    preComputedValues={currentMetricValues}
                  />
                </div>
              </div>

              {/* Calendar Section */}
              <DashboardSection title="Trading Calendar" className="dashboard-calendar" data-section="calendar">
                <TradingCalendar
                  accountId={selectedAccountId} // Pass accountId as a regular prop
                  trades={filteredTrades} // Pass filtered trades to respect date range and other filters
                  userId={userId || undefined}
                  dashboardFilters={dashboardFilters}
                  onSelectDate={(date, trades) => {
                    setSelectedDate(date)
                    setSelectedTrades(trades || [])
                    setIsTradeDetailsOpen(true)
                  }}
                />
              </DashboardSection>
            </>
          )}
        </>
      )}

      {/* Trade Details Dialog */}
      <TradeDetailsDialog
        isOpen={isTradeDetailsOpen}
        onOpenChange={setIsTradeDetailsOpen}
        selectedTrades={selectedTrades || []}
        selectedDate={selectedDate}
        tradesPerPage={tradesPerPage}
      />
    </div>
  )
}
