"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { format, addDays } from "date-fns"
import { Goal, CustomMetric } from "@/types/metrics"
import { createGoal, updateGoal, createCustomMetric } from "@/lib/metrics-service"
import { GOAL_TEMPLATES, GOAL_CATEGORIES, getGoalTemplatesByCategory, getLinkedMetricTemplate, GoalTemplate } from "@/lib/goal-templates"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Target, Sparkles, Plus, Lightbulb } from "lucide-react"
import { cn } from "@/lib/utils"

// Define the form schema with enforced 2 decimal precision
const formSchema = z.object({
  title: z.string().min(1, "Title is required").max(100, "Title must be less than 100 characters"),
  description: z.string().optional(),
  metric_id: z.string().optional(),
  target_value: z.coerce.number().min(0, "Target value must be positive"),
  start_date: z.date(),
  end_date: z.date(),
}).refine(data => data.end_date >= data.start_date, {
  message: "End date must be after start date",
  path: ["end_date"],
});

type FormValues = z.infer<typeof formSchema>

interface EnhancedGoalFormProps {
  userId: string
  accountId?: string | null
  goal?: Goal
  metrics: CustomMetric[]
  onSuccess?: (goal: Goal) => void
  onCancel?: () => void
  onMetricCreated?: (metric: CustomMetric) => void
}

export function EnhancedGoalForm({
  userId,
  accountId,
  goal,
  metrics,
  onSuccess,
  onCancel,
  onMetricCreated
}: EnhancedGoalFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<GoalTemplate | null>(null)
  const [creationMode, setCreationMode] = useState<'template' | 'custom'>('template')
  const [isCreatingMetric, setIsCreatingMetric] = useState(false)

  const isEditing = !!goal

  // Initialize form with default values or existing goal values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: goal?.title || "",
      description: goal?.description || "",
      metric_id: goal?.metric_id || "none",
      target_value: goal?.target_value || 0,
      start_date: goal?.start_date ? new Date(goal.start_date) : new Date(),
      end_date: goal?.end_date ? new Date(goal.end_date) : addDays(new Date(), 30),
    },
  })

  // Handle template selection
  const handleTemplateSelect = async (template: GoalTemplate) => {
    setSelectedTemplate(template)
    form.setValue("title", template.title)
    form.setValue("description", template.description)
    form.setValue("target_value", template.targetValue)
    form.setValue("end_date", addDays(new Date(), template.timeframe))

    // Check if linked metric exists, if not offer to create it
    const linkedMetricTemplate = getLinkedMetricTemplate(template)
    if (linkedMetricTemplate) {
      const existingMetric = metrics.find(m => m.formula === linkedMetricTemplate.formula)
      if (existingMetric) {
        form.setValue("metric_id", existingMetric.id)
      } else {
        // Offer to create the metric
        setIsCreatingMetric(true)
      }
    }
  }

  // Handle creating linked metric
  const handleCreateLinkedMetric = async () => {
    if (!selectedTemplate) return

    const linkedMetricTemplate = getLinkedMetricTemplate(selectedTemplate)
    if (!linkedMetricTemplate) return

    try {
      const newMetric = await createCustomMetric(userId, {
        name: linkedMetricTemplate.name,
        description: linkedMetricTemplate.description,
        formula: linkedMetricTemplate.formula,
        is_percentage: linkedMetricTemplate.is_percentage,
        display_precision: 2,
        is_higher_better: linkedMetricTemplate.is_higher_better,
        target_value: selectedTemplate.targetValue,
      }, accountId)

      if (newMetric) {
        form.setValue("metric_id", newMetric.id)
        setIsCreatingMetric(false)
        toast.success(`Created metric: ${newMetric.name}`)
        if (onMetricCreated) {
          onMetricCreated(newMetric)
        }
      }
    } catch (error) {
      console.error("Error creating linked metric:", error)
      toast.error("Failed to create linked metric")
    }
  }

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      const goalData = {
        ...values,
        metric_id: values.metric_id === "none" || !values.metric_id ? null : values.metric_id,
        start_date: format(values.start_date, "yyyy-MM-dd"),
        end_date: format(values.end_date, "yyyy-MM-dd"),
        current_value: goal?.current_value || 0,
        is_completed: goal?.is_completed || false,
        target_value: parseFloat(values.target_value.toFixed(2)), // Ensure 2 decimal precision
      }

      let result: Goal | null

      if (isEditing && goal) {
        result = await updateGoal(userId, goal.id, goalData)
        if (result) {
          toast.success("Goal updated successfully")
        }
      } else {
        result = await createGoal(userId, goalData, accountId)
        if (result) {
          toast.success("Goal created successfully")
          form.reset()
          setSelectedTemplate(null)
        }
      }

      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (error) {
      console.error("Error saving goal:", error)
      toast.error("Failed to save goal")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          {isEditing ? "Edit Goal" : "Create New Goal"}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? "Update your trading goal"
            : "Set a new goal to improve your trading performance. Choose from proven templates or create a custom goal."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!isEditing && (
          <Tabs value={creationMode} onValueChange={(value) => setCreationMode(value as 'template' | 'custom')} className="mb-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="template" className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Use Template
              </TabsTrigger>
              <TabsTrigger value="custom" className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Custom Goal
              </TabsTrigger>
            </TabsList>

            <TabsContent value="template" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Choose a Goal Template</h3>
                <p className="text-sm text-muted-foreground">
                  Select from time-tested trading goals. Each template includes recommended targets and actionable tips.
                </p>
                
                {GOAL_CATEGORIES.map((category) => (
                  <div key={category.id} className="space-y-3">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{category.icon}</span>
                      <h4 className="font-medium">{category.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {getGoalTemplatesByCategory(category.id).length} goals
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{category.description}</p>
                    
                    <div className="grid gap-3 md:grid-cols-2">
                      {getGoalTemplatesByCategory(category.id).map((template) => (
                        <Card 
                          key={template.id} 
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedTemplate?.id === template.id ? 'ring-2 ring-primary' : ''
                          }`}
                          onClick={() => handleTemplateSelect(template)}
                        >
                          <CardContent className="p-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h5 className="font-medium">{template.title}</h5>
                                <Badge 
                                  variant={template.priority === 'high' ? 'destructive' : 
                                          template.priority === 'medium' ? 'default' : 'secondary'} 
                                  className="text-xs"
                                >
                                  {template.priority}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{template.description}</p>
                              <div className="flex items-center gap-2 text-xs">
                                <span className="font-medium">Target:</span>
                                <span>{template.targetValue.toFixed(2)}</span>
                                <span className="text-muted-foreground">•</span>
                                <span>{template.timeframe} days</span>
                              </div>
                              <p className="text-xs text-muted-foreground">{template.explanation}</p>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        )}

        {/* Metric Creation Prompt */}
        {isCreatingMetric && selectedTemplate && (
          <Card className="mb-6 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Lightbulb className="h-5 w-5 text-amber-600 mt-0.5" />
                <div className="space-y-2 flex-1">
                  <h4 className="font-medium text-amber-900 dark:text-amber-100">
                    Create Required Metric
                  </h4>
                  <p className="text-sm text-amber-800 dark:text-amber-200">
                    This goal requires the "{getLinkedMetricTemplate(selectedTemplate)?.name}" metric. 
                    Would you like to create it automatically?
                  </p>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleCreateLinkedMetric}>
                      <Plus className="h-4 w-4 mr-1" />
                      Create Metric
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setIsCreatingMetric(false)}>
                      Skip
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {selectedTemplate && (
          <Card className="mb-6 bg-muted/50">
            <CardContent className="p-4">
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Goal Template Information
                </h4>
                <p className="text-sm">{selectedTemplate.explanation}</p>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Success Tips:</p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    {selectedTemplate.tips.map((tip, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary">•</span>
                        <span>{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Goal Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Achieve 70% win rate" {...field} />
                    </FormControl>
                    <FormDescription>
                      A clear, specific title for your goal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="target_value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Value</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="70.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Target value (displayed with 2 decimal places)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Maintain a 70% win rate to improve overall profitability"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional description explaining your goal
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metric_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Linked Metric (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value || "none"}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a metric to track progress" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">No linked metric</SelectItem>
                      {metrics.map((metric) => (
                        <SelectItem key={metric.id} value={metric.id}>
                          {metric.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Link this goal to a metric for automatic progress tracking
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      When you want to start working on this goal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Target Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < form.getValues("start_date")
                          }
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      When you want to achieve this goal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-between pt-6">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Update Goal"
                  : "Create Goal"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
