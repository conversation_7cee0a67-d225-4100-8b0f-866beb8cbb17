"use client"

import { CustomMetric, Goal } from '@/types/metrics';
import { Trade } from '@/types/trade';
import MetricsGoalsClient from './client';

interface ClientWrapperProps {
  userId: string;
  selectedAccountId: string | null;
  initialMetrics: CustomMetric[];
  initialGoals: Goal[];
  initialTrades: Trade[];
  initialMetricValues: Record<string, number>;
}

export default function ClientWrapper({ 
  userId, 
  selectedAccountId, 
  initialMetrics, 
  initialGoals, 
  initialTrades,
  initialMetricValues
}: ClientWrapperProps) {
  return (
    <MetricsGoalsClient
      userId={userId}
      selectedAccountId={selectedAccountId}
      initialMetrics={initialMetrics}
      initialGoals={initialGoals}
      initialTrades={initialTrades}
      initialMetricValues={initialMetricValues}
    />
  );
}
