"use client"

import { useState, useEffect } from "react"
import { CustomMetric, Goal } from "@/types/metrics"
import { Trade } from "@/types/trade"
import { calculateMetricValue } from "@/lib/metrics-service"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowUpRight, ArrowDownRight, TrendingUp, Target } from "lucide-react"

interface MetricsDashboardProps {
  userId: string
  accountId: string | null
  metrics: CustomMetric[]
  goals: Goal[]
  trades: any[] // Using any[] to accommodate different trade object structures
  onViewAll?: () => void
}

export function MetricsDashboard({ userId, accountId, metrics, goals, trades, onViewAll }: MetricsDashboardProps) {
  const [activeTab, setActiveTab] = useState<"metrics" | "goals">("metrics")
  const [metricValues, setMetricValues] = useState<Record<string, number>>({})

  // Calculate metric values when trades or metrics change
  useEffect(() => {
    const calculateValues = async () => {
      const values: Record<string, number> = {}

      for (const metric of metrics) {
        values[metric.id] = await calculateMetricValue(metric, trades, accountId || undefined)
      }

      setMetricValues(values)
    }

    calculateValues()
  }, [metrics, trades, accountId])

  // Format a metric value with appropriate units (always 2 decimal places)
  const formatMetricValue = (metric: CustomMetric) => {
    const value = metricValues[metric.id] ?? 0
    const formattedValue = value.toFixed(2) // Always 2 decimal places for consistency
    return metric.is_percentage ? `${formattedValue}%` : formattedValue
  }

  // Calculate progress percentage for a goal
  const calculateGoalProgress = (goal: Goal) => {
    if (goal.is_completed) return 100

    const metric = goal.metric_id ? metrics.find(m => m.id === goal.metric_id) : null
    const currentValue = goal.metric_id ? (metricValues[goal.metric_id] ?? goal.current_value) : goal.current_value

    if (!metric) {
      // If no metric is associated, use a simple percentage of current/target
      return Math.min(Math.round((currentValue / goal.target_value) * 100), 100)
    }

    // For metrics where lower is better, invert the progress calculation
    if (!metric.is_higher_better) {
      if (currentValue <= goal.target_value) return 100
      return Math.max(0, Math.round((goal.target_value / currentValue) * 100))
    }

    // For metrics where higher is better
    if (currentValue >= goal.target_value) return 100
    return Math.min(100, Math.round((currentValue / goal.target_value) * 100))
  }

  // Calculate intelligent progress for metrics
  const getMetricProgress = (metric: CustomMetric): number => {
    if (!metric.target_value) return 0

    const value = metricValues[metric.id] ?? 0

    if (metric.is_higher_better) {
      // For higher-is-better metrics: progress = (current / target) * 100
      return Math.min(100, Math.max(0, (value / metric.target_value) * 100))
    } else {
      // For lower-is-better metrics: progress = ((target - current) / target) * 100, capped at 100%
      if (value <= metric.target_value) {
        return 100 // Achieved target
      } else {
        // Calculate how much over target we are, but cap progress at 0%
        const overTarget = value - metric.target_value
        const progressLoss = (overTarget / metric.target_value) * 100
        return Math.max(0, 100 - progressLoss)
      }
    }
  }

  // Get the status of a metric compared to its target
  const getMetricStatus = (metric: CustomMetric) => {
    if (metric.target_value === null || metric.target_value === undefined) return null

    const progress = getMetricProgress(metric)

    if (progress >= 100) return 'success'
    if (progress >= 80) return 'on_track'
    if (progress >= 50) return 'behind'
    return 'at_risk'
  }

  return (
    <Card className="dashboard-card bg-card hover:bg-card transition-all duration-300 h-[352px] flex flex-col">
      <CardHeader className="pb-2 flex flex-row items-center justify-between flex-shrink-0">
        <CardTitle className="text-base font-medium">Custom Metrics & Goals</CardTitle>
        {onViewAll && (
          <Button
            variant="outline"
            size="sm"
            onClick={onViewAll}
          >
            View All
          </Button>
        )}
      </CardHeader>
      <CardContent className="p-0 flex-grow flex flex-col overflow-hidden">
        <Tabs
          defaultValue="metrics"
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as "metrics" | "goals")}
          className="flex flex-col h-full"
        >
          <TabsList className="grid w-full grid-cols-2 rounded-none border-b bg-transparent flex-shrink-0">
            <TabsTrigger
              value="metrics"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              Metrics
            </TabsTrigger>
            <TabsTrigger
              value="goals"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              <Target className="mr-2 h-4 w-4" />
              Goals
            </TabsTrigger>
          </TabsList>

          {/* Metrics Tab */}
          <TabsContent
            value="metrics"
            className="flex-grow overflow-hidden flex flex-col"
          >
            <div className="px-4 py-4 flex-grow overflow-y-auto">
              {metrics.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  <p>You haven't created any custom metrics yet.</p>
                  <p className="text-sm mt-1">
                    Go to the Metrics & Goals page to create your first metric.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4 pb-4">
                  {metrics.map((metric) => {
                    const value = metricValues[metric.id] ?? 0
                    const status = getMetricStatus(metric)

                    return (
                      <div key={metric.id}>
                        <div className="flex items-start justify-between">
                          <div className="text-xs text-muted-foreground">
                            {metric.name}
                            {metric.is_higher_better ? (
                              <ArrowUpRight className="h-3 w-3 text-green-500 inline ml-1" />
                            ) : (
                              <ArrowDownRight className="h-3 w-3 text-red-500 inline ml-1" />
                            )}
                          </div>
                          {status !== null && (
                            <Badge
                              variant={
                                status === 'success' ? "default" :
                                status === 'on_track' ? "secondary" :
                                "destructive"
                              }
                              className={`scale-75 h-4 ${
                                status === 'success' ? "bg-green-500 text-white" :
                                status === 'on_track' ? "bg-blue-500 text-white" :
                                status === 'behind' ? "bg-amber-500 text-white" : ""
                              }`}
                            >
                              {status === 'success' ? "Target" :
                               status === 'on_track' ? "On Track" :
                               status === 'behind' ? "Behind" : "At Risk"}
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm font-medium">
                          {formatMetricValue(metric)}
                          {metric.target_value !== null && metric.target_value !== undefined && (
                            <span className="text-xs text-muted-foreground ml-2">
                              (Target: {metric.target_value.toFixed(2)}
                              {metric.is_percentage && "%"})
                            </span>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Goals Tab */}
          <TabsContent
            value="goals"
            className="flex-grow overflow-hidden flex flex-col"
          >
            <div className="px-4 py-4 flex-grow overflow-y-auto">
              {goals.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  <p>You haven't set any trading goals yet.</p>
                  <p className="text-sm mt-1">
                    Go to the Metrics & Goals page to set your first goal.
                  </p>
                </div>
              ) : (
                <div className="space-y-2 pb-4">
                  {goals.map((goal) => {
                    const progress = calculateGoalProgress(goal)
                    const metric = goal.metric_id ? metrics.find(m => m.id === goal.metric_id) : null
                    const currentValue = goal.metric_id ? (metricValues[goal.metric_id] ?? goal.current_value) : goal.current_value

                    return (
                      <div key={goal.id} className={`border rounded-md p-2 ${goal.is_completed ? "border-green-500/20" : "border-border"}`}>
                        <div className="flex justify-between items-start mb-0.5">
                          <div className="truncate mr-2">
                            <h3 className="font-medium text-xs truncate">{goal.title}</h3>
                            {metric && (
                              <p className="text-[10px] text-muted-foreground truncate">
                                Tracking: {metric.name}
                              </p>
                            )}
                          </div>
                          <Badge variant={goal.is_completed ? "default" : "outline"} className={`shrink-0 text-[10px] h-5 px-1.5 ${goal.is_completed ? "bg-green-500 text-white" : ""}`}>
                            {goal.is_completed ? "Completed" : "In Progress"}
                          </Badge>
                        </div>

                        <div className="space-y-0.5">
                          <div className="flex justify-between text-[10px]">
                            <span>Progress</span>
                            <span className="text-right">
                              {metric ? (
                                <>
                                  {currentValue.toFixed(2)}
                                  {metric.is_percentage && "%"} /
                                  {goal.target_value.toFixed(2)}
                                  {metric.is_percentage && "%"}
                                </>
                              ) : (
                                <>
                                  {currentValue.toFixed(2)} / {goal.target_value.toFixed(2)}
                                </>
                              )}
                            </span>
                          </div>
                          <Progress value={progress} className="h-1" />
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
