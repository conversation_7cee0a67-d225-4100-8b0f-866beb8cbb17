"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { getSupabaseBrowser } from "@/lib/supabase"
import { CustomMetric, Goal } from "@/types/metrics"
import { Trade } from "@/types/trade"
import { getCustomMetrics, getGoals, calculateMetricValue } from "@/lib/metrics-service"
import { getTrades } from "@/lib/trade-service"
import { useAccount } from "@/contexts/account-context"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Target, TrendingUp } from "lucide-react"

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { EnhancedMetricsList } from "@/components/metrics-goals/enhanced-metrics-list"
import { EnhancedMetricForm } from "@/components/metrics-goals/enhanced-metric-form"
import { EnhancedGoalsList } from "@/components/metrics-goals/enhanced-goals-list"
import { EnhancedGoalForm } from "@/components/metrics-goals/enhanced-goal-form"

interface MetricsGoalsClientProps {
  userId: string;
  selectedAccountId: string | null;
  initialMetrics: CustomMetric[];
  initialGoals: Goal[];
  initialTrades: Trade[];
  initialMetricValues: Record<string, number>;
}

export default function MetricsGoalsClient({
  userId,
  selectedAccountId,
  initialMetrics,
  initialGoals,
  initialTrades,
  initialMetricValues
}: MetricsGoalsClientProps) {
  const router = useRouter()
  const supabase = getSupabaseBrowser()
  const { selectedAccountId: contextAccountId } = useAccount()

  const [metrics, setMetrics] = useState<CustomMetric[]>(initialMetrics)
  const [goals, setGoals] = useState<Goal[]>(initialGoals)
  const [trades, setTrades] = useState<Trade[]>(initialTrades)
  const [metricValues, setMetricValues] = useState<Record<string, number>>(initialMetricValues)
  const [activeTab, setActiveTab] = useState<"metrics" | "goals">("metrics")
  const [showMetricForm, setShowMetricForm] = useState(false)
  const [showGoalForm, setShowGoalForm] = useState(false)
  const [editingMetric, setEditingMetric] = useState<CustomMetric | null>(null)
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Use the context account ID for real-time updates, but fall back to the initial one
  const currentAccountId = contextAccountId !== undefined ? contextAccountId : selectedAccountId

  // Reload data when account changes (but not on initial load since we have SSR data)
  useEffect(() => {
    if (contextAccountId !== undefined && contextAccountId !== selectedAccountId) {
      const loadData = async () => {
        setIsLoading(true)
        try {
          // Load metrics - filter by selected account
          const metricsData = await getCustomMetrics(userId, contextAccountId)
          setMetrics(metricsData)

          // Load goals - filter by selected account
          const goalsData = await getGoals(userId, contextAccountId)
          setGoals(goalsData)

          // Load trades for metric calculations - filter by selected account
          const tradesData = await getTrades(userId, contextAccountId)
          setTrades(tradesData)

          // Recalculate metric values
          const values: Record<string, number> = {}
          for (const metric of metricsData) {
            values[metric.id] = await calculateMetricValue(metric, tradesData, contextAccountId || undefined)
          }
          setMetricValues(values)
        } catch (error) {
          console.error("Error loading data:", error)
          toast.error("Failed to load metrics and goals")
        } finally {
          setIsLoading(false)
        }
      }

      loadData()
    }
  }, [userId, contextAccountId, selectedAccountId])

  // Handle adding a new metric
  const handleAddMetric = () => {
    setEditingMetric(null)
    setShowMetricForm(true)
    setActiveTab("metrics")
  }

  // Handle editing a metric
  const handleEditMetric = (metric: CustomMetric) => {
    setEditingMetric(metric)
    setShowMetricForm(true)
    setActiveTab("metrics")
  }

  // Handle metric form submission
  const handleMetricSuccess = async (metric: CustomMetric) => {
    if (editingMetric) {
      // Update existing metric in the list
      setMetrics(metrics.map(m => m.id === metric.id ? metric : m))
    } else {
      // Add new metric to the list
      setMetrics([metric, ...metrics])
    }

    // Recalculate the metric value
    const value = await calculateMetricValue(metric, trades, currentAccountId || undefined)
    setMetricValues(prev => ({ ...prev, [metric.id]: value }))

    setShowMetricForm(false)
    setEditingMetric(null)
  }

  // Handle metric deletion
  const handleDeleteMetric = (metricId: string) => {
    setMetrics(metrics.filter(m => m.id !== metricId))
    setMetricValues(prev => {
      const newValues = { ...prev }
      delete newValues[metricId]
      return newValues
    })
  }

  // Handle adding a new goal
  const handleAddGoal = () => {
    setEditingGoal(null)
    setShowGoalForm(true)
    setActiveTab("goals")
  }

  // Handle editing a goal
  const handleEditGoal = (goal: Goal) => {
    setEditingGoal(goal)
    setShowGoalForm(true)
    setActiveTab("goals")
  }

  // Handle goal form submission
  const handleGoalSuccess = (goal: Goal) => {
    if (editingGoal) {
      // Update existing goal in the list
      setGoals(goals.map(g => g.id === goal.id ? goal : g))
    } else {
      // Add new goal to the list
      setGoals([goal, ...goals])
    }

    setShowGoalForm(false)
    setEditingGoal(null)
  }

  // Handle metric created from goal form
  const handleMetricCreatedFromGoal = async (metric: CustomMetric) => {
    setMetrics([metric, ...metrics])
    
    // Calculate the metric value
    const value = await calculateMetricValue(metric, trades, currentAccountId || undefined)
    setMetricValues(prev => ({ ...prev, [metric.id]: value }))
  }

  // Handle viewing metric from goals
  const handleViewMetricFromGoal = (metricId: string) => {
    setActiveTab("metrics")
    // Could add highlighting logic here
  }

  // Handle goal deletion
  const handleDeleteGoal = (goalId: string) => {
    setGoals(goals.filter(g => g.id !== goalId))
  }

  if (currentAccountId === null) {
    return (
      <div className="container py-10">
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your metrics and goals. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-10">
      <p className="text-muted-foreground mb-8">
        Create custom metrics to track your trading performance and set goals to improve your results.
      </p>

      <Tabs defaultValue="metrics" value={activeTab} onValueChange={(value) => setActiveTab(value as "metrics" | "goals")}>
        <TabsList className="grid w-full grid-cols-2 rounded-none border-b bg-muted/30 dark:bg-transparent mb-8">
          <TabsTrigger
            value="metrics"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium data-[state=active]:shadow-sm"
          >
            <TrendingUp className="mr-2 h-4 w-4" />
            Custom Metrics
          </TabsTrigger>
          <TabsTrigger
            value="goals"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium data-[state=active]:shadow-sm"
          >
            <Target className="mr-2 h-4 w-4" />
            Trading Goals
          </TabsTrigger>
        </TabsList>

        <TabsContent value="metrics">
          {showMetricForm ? (
            <EnhancedMetricForm
              userId={userId}
              accountId={currentAccountId}
              metric={editingMetric || undefined}
              onSuccess={handleMetricSuccess}
              onCancel={() => {
                setShowMetricForm(false)
                setEditingMetric(null)
              }}
            />
          ) : (
            <EnhancedMetricsList
              userId={userId}
              accountId={currentAccountId}
              metrics={metrics}
              goals={goals}
              trades={trades}
              onEdit={handleEditMetric}
              onDelete={handleDeleteMetric}
              onAdd={handleAddMetric}
              onViewGoals={() => setActiveTab("goals")}
              preComputedValues={metricValues}
            />
          )}
        </TabsContent>

        <TabsContent value="goals">
          {showGoalForm ? (
            <EnhancedGoalForm
              userId={userId}
              accountId={currentAccountId}
              goal={editingGoal || undefined}
              metrics={metrics}
              onSuccess={handleGoalSuccess}
              onCancel={() => {
                setShowGoalForm(false)
                setEditingGoal(null)
              }}
              onMetricCreated={handleMetricCreatedFromGoal}
            />
          ) : (
            <EnhancedGoalsList
              userId={userId}
              accountId={currentAccountId}
              goals={goals}
              metrics={metrics}
              trades={trades}
              onEdit={handleEditGoal}
              onDelete={handleDeleteGoal}
              onAdd={handleAddGoal}
              onViewMetric={handleViewMetricFromGoal}
              preComputedValues={metricValues}
            />
          )}
        </TabsContent>
      </Tabs>

      <Separator className="my-10" />

      <div className="space-y-6">
        <h2 className="text-2xl font-bold tracking-tight">How It Works</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Custom Metrics</h3>
            <p className="text-muted-foreground">
              Create personalized metrics to track specific aspects of your trading performance.
            </p>
            <ul className="space-y-2 list-disc pl-5">
              <li>Define formulas using trading variables like win rate, profit, etc.</li>
              <li>Set target values to track your progress</li>
              <li>Visualize your metrics on the dashboard</li>
              <li>Use metrics to identify strengths and weaknesses</li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Trading Goals</h3>
            <p className="text-muted-foreground">
              Set specific, measurable goals to improve your trading performance over time.
            </p>
            <ul className="space-y-2 list-disc pl-5">
              <li>Link goals to custom metrics for automatic tracking</li>
              <li>Set target dates to stay accountable</li>
              <li>Track your progress visually</li>
              <li>Celebrate achievements when you reach your goals</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
