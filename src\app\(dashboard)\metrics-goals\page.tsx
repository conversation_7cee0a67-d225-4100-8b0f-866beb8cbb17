import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import {
  getServerCustomMetrics,
  getServerGoals,
  getAccountTrades,
  getSelectedAccountId,
  calculateServerMetricValue
} from '@/lib/dashboard-server-utils';
import { Suspense } from 'react';
import MetricsGoalsClient from './client';

export default async function MetricsGoalsPage() {
  console.log('[SSR] MetricsGoalsPage: Starting server-side rendering...');

  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  console.log('[SSR] MetricsGoalsPage: Fetching authenticated user...');
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    console.log('[SSR] MetricsGoalsPage: No authenticated user, redirecting to auth');
    redirect('/auth');
  }

  const userId = user.id;
  console.log('[SSR] MetricsGoalsPage: Authenticated user ID:', userId);

  // Get selected account ID
  console.log('[SSR] MetricsGoalsPage: Getting selected account ID...');
  const selectedAccountId = await getSelectedAccountId(userId);
  console.log('[SSR] MetricsGoalsPage: Selected account ID:', selectedAccountId);

  // Fetch initial data server-side
  let initialMetrics: any[] = [];
  let initialGoals: any[] = [];
  let initialTrades: any[] = [];
  let initialMetricValues: Record<string, number> = {};

  if (selectedAccountId) {
    console.log('[SSR] MetricsGoalsPage: Fetching server-side data for account:', selectedAccountId);

    // Fetch metrics
    console.log('[SSR] MetricsGoalsPage: Fetching custom metrics...');
    const { metrics } = await getServerCustomMetrics(userId, selectedAccountId);
    initialMetrics = metrics;
    console.log('[SSR] MetricsGoalsPage: Found', metrics.length, 'custom metrics');

    // Fetch goals
    console.log('[SSR] MetricsGoalsPage: Fetching goals...');
    const { goals } = await getServerGoals(userId, selectedAccountId);
    initialGoals = goals;
    console.log('[SSR] MetricsGoalsPage: Found', goals.length, 'goals');

    // Fetch trades
    console.log('[SSR] MetricsGoalsPage: Fetching trades...');
    const { trades } = await getAccountTrades(selectedAccountId);
    initialTrades = trades;
    console.log('[SSR] MetricsGoalsPage: Found', trades.length, 'trades');

    // Pre-calculate metric values
    console.log('[SSR] MetricsGoalsPage: Pre-calculating metric values...');
    for (const metric of initialMetrics) {
      const value = await calculateServerMetricValue(metric, initialTrades, selectedAccountId);
      initialMetricValues[metric.id] = value;
      console.log('[SSR] MetricsGoalsPage: Calculated metric', metric.name, '=', value);
    }
    console.log('[SSR] MetricsGoalsPage: Pre-calculated', Object.keys(initialMetricValues).length, 'metric values');
  } else {
    console.log('[SSR] MetricsGoalsPage: No account selected, using empty data');
  }

  // Render the client component with the fetched data
  console.log('[SSR] MetricsGoalsPage: Rendering client component with SSR data');
  console.log('[SSR] MetricsGoalsPage: Passing', Object.keys(initialMetricValues).length, 'pre-computed metric values');

  return (
    <MetricsGoalsClient
      userId={userId}
      selectedAccountId={selectedAccountId}
      initialMetrics={initialMetrics}
      initialGoals={initialGoals}
      initialTrades={initialTrades}
      initialMetricValues={initialMetricValues}
    />
  );
}
