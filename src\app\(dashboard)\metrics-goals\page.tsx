import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import {
  getServerCustomMetrics,
  getServerGoals,
  getAccountTrades,
  getSelectedAccountId,
  calculateServerMetricValue
} from '@/lib/dashboard-server-utils';
import ClientWrapper from './client-wrapper';

export default async function MetricsGoalsPage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/auth');
  }

  const userId = user.id;

  // Get selected account ID
  const selectedAccountId = await getSelectedAccountId(userId);

  // Fetch initial data server-side
  let initialMetrics: any[] = [];
  let initialGoals: any[] = [];
  let initialTrades: any[] = [];
  let initialMetricValues: Record<string, number> = {};

  if (selectedAccountId) {
    // Fetch metrics
    const { metrics } = await getServerCustomMetrics(userId, selectedAccountId);
    initialMetrics = metrics;

    // Fetch goals
    const { goals } = await getServerGoals(userId, selectedAccountId);
    initialGoals = goals;

    // Fetch trades
    const { trades } = await getAccountTrades(selectedAccountId);
    initialTrades = trades;

    // Pre-calculate metric values
    for (const metric of initialMetrics) {
      initialMetricValues[metric.id] = await calculateServerMetricValue(metric, initialTrades, selectedAccountId);
    }
  }

  // Render the client component with the fetched data
  return (
    <ClientWrapper
      userId={userId}
      selectedAccountId={selectedAccountId}
      initialMetrics={initialMetrics}
      initialGoals={initialGoals}
      initialTrades={initialTrades}
      initialMetricValues={initialMetricValues}
    />
  );
}
